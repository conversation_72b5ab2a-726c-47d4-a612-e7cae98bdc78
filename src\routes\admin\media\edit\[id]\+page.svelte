<script>
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';

  // Define types for media items
  /** @typedef {{
    id: number;
    originalName: string;
    path: string;
    thumbnailPath?: string;
    type: 'image' | 'video' | 'document';
    mimeType: string;
    size: number;
    alt?: string;
    caption?: string;
    createdAt: string;
    updatedAt: string;
  }} MediaItem */

  // Get the media item ID from the URL
  export let data;
  const { id } = data;

  // State variables
  /** @type {MediaItem|null} */
  let mediaItem = null;
  let loading = true;
  let saving = false;
  /** @type {string|null} */
  let error = null;
  /** @type {string|null} */
  let successMessage = null;

  // Form data
  let alt = '';
  let caption = '';

  // Fetch the media item from the API
  async function fetchMediaItem() {
    try {
      loading = true;
      error = null;

      const response = await fetch(`/api/media/${id}`);

      if (!response.ok) {
        throw new Error('Failed to fetch media item');
      }

      const data = await response.json();
      mediaItem = data.data;

      // Populate form fields if mediaItem exists
      if (mediaItem) {
        alt = mediaItem.alt || '';
        caption = mediaItem.caption || '';
      }
    } catch (err) {
      console.error('Error fetching media item:', err);
      error = err instanceof Error ? err.message : String(err);
    } finally {
      loading = false;
    }
  }

  // Update the media item
  async function updateMediaItem() {
    try {
      saving = true;
      error = null;
      successMessage = null;

      const response = await fetch(`/api/media/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          alt,
          caption
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update media item');
      }

      const data = await response.json();
      mediaItem = data.data;

      successMessage = 'Media item updated successfully!';

      // Hide success message after 3 seconds
      setTimeout(() => {
        successMessage = null;
      }, 3000);
    } catch (err) {
      console.error('Error updating media item:', err);
      error = err instanceof Error ? err.message : String(err);
    } finally {
      saving = false;
    }
  }

  /**
   * Handle form submission
   * @param {Event} event - The form submit event
   */
  function handleSubmit(event) {
    event.preventDefault();
    updateMediaItem();
  }

  /**
   * Handle image loading errors by setting a fallback image
   * @param {Event} event - The error event
   */
  function handleImageError(event) {
    // We know this is an image element
    const img = event.target;
    if (img && 'src' in img) {
      img.src = '/images/placeholder.jpg';
    }
  }

  // Load media item on mount
  onMount(() => {
    fetchMediaItem();
  });
</script>

<svelte:head>
  <title>Edit Media Item - Admin</title>
</svelte:head>

<div class="edit-container">
  <div class="header">
    <h1>Edit Media Item</h1>
    <a href="/admin/media" class="btn secondary">Back to Media Library</a>
  </div>

  {#if error}
    <div class="error-message">
      <p>{error}</p>
    </div>
  {/if}

  {#if successMessage}
    <div class="success-message">
      <p>{successMessage}</p>
    </div>
  {/if}

  {#if loading}
    <div class="loading">
      <p>Loading media item...</p>
    </div>
  {:else if mediaItem}
    <div class="edit-form-container">
      <div class="media-preview">
        {#if mediaItem.type === 'image'}
          <img
            src={mediaItem.path || '/images/placeholder.jpg'}
            alt={mediaItem.alt || mediaItem.originalName}
            on:error={handleImageError}
          />
        {:else if mediaItem.type === 'video'}
          <video controls>
            <source src={mediaItem.path} type={mediaItem.mimeType}>
            <track kind="captions" label="English captions" src="" default>
            Your browser does not support the video tag.
          </video>
          <p class="caption-note">Note: Captions not available for this video.</p>
        {:else}
          <div class="document-preview">
            <div class="document-icon">📄</div>
            <div class="document-name">{mediaItem.originalName}</div>
          </div>
        {/if}

        <div class="media-details">
          <p><strong>Filename:</strong> {mediaItem.originalName}</p>
          <p><strong>Type:</strong> {mediaItem.mimeType}</p>
          <p><strong>Size:</strong> {Math.round(mediaItem.size / 1024)} KB</p>
          <p><strong>Uploaded:</strong> {new Date(mediaItem.createdAt).toLocaleString()}</p>
          <p>
            <strong>URL:</strong>
            <a href={mediaItem.path} target="_blank" rel="noopener noreferrer">
              {mediaItem.path}
            </a>
          </p>
        </div>
      </div>

      <form on:submit={handleSubmit} class="edit-form">
        <div class="form-group">
          <label for="alt">Alt Text</label>
          <input
            type="text"
            id="alt"
            bind:value={alt}
            placeholder="Brief description of the media (for accessibility)"
            disabled={saving}
          />
          <small>Describe the media for users who can't see it (important for accessibility)</small>
        </div>

        <div class="form-group">
          <label for="caption">Caption</label>
          <textarea
            id="caption"
            bind:value={caption}
            rows="4"
            placeholder="Optional caption or description"
            disabled={saving}
          ></textarea>
          <small>Additional information or context about this media item</small>
        </div>

        <div class="form-actions">
          <button type="submit" class="btn primary" disabled={saving}>
            {#if saving}
              Saving...
            {:else}
              Save Changes
            {/if}
          </button>
          <button
            type="button"
            class="btn danger"
            disabled={saving}
            on:click={() => {
              if (confirm('Are you sure you want to delete this media item?')) {
                fetch(`/api/media/${id}`, { method: 'DELETE' })
                  .then(() => goto('/admin/media'))
                  .catch(err => {
                    console.error('Error deleting media item:', err);
                    error = 'Failed to delete media item';
                  });
              }
            }}
          >
            Delete Item
          </button>
        </div>
      </form>
    </div>
  {:else}
    <div class="not-found">
      <p>Media item not found.</p>
      <a href="/admin/media" class="btn primary">Back to Media Library</a>
    </div>
  {/if}
</div>

<style>
  .edit-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .primary {
    background-color: #4caf50;
    color: white;
  }

  .secondary {
    background-color: #f0f0f0;
    color: #333;
  }

  .danger {
    background-color: #f44336;
    color: white;
  }

  .error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
  }

  .success-message {
    background-color: #e8f5e9;
    color: #2e7d32;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
  }

  .loading, .not-found {
    text-align: center;
    padding: 3rem;
    background-color: #f5f5f5;
    border-radius: 4px;
  }

  .not-found {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .edit-form-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }

  .media-preview {
    background-color: #f5f5f5;
    border-radius: 8px;
    overflow: hidden;
    padding: 1rem;
  }

  .media-preview img, .media-preview video {
    width: 100%;
    max-height: 300px;
    object-fit: contain;
    display: block;
    margin-bottom: 1rem;
    background-color: #fff;
    border-radius: 4px;
  }

  .caption-note {
    font-size: 0.8rem;
    color: #666;
    font-style: italic;
    margin-top: -0.5rem;
    margin-bottom: 1rem;
    text-align: center;
  }

  .document-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 1rem;
  }

  .document-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
  }

  .document-name {
    font-size: 1rem;
    text-align: center;
    word-break: break-all;
    padding: 0 1rem;
  }

  .media-details {
    font-size: 0.9rem;
  }

  .media-details p {
    margin: 0.5rem 0;
  }

  .media-details a {
    color: #2196f3;
    word-break: break-all;
  }

  .edit-form {
    background-color: #f9f9f9;
    padding: 1.5rem;
    border-radius: 8px;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
  }

  input[type="text"],
  textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
  }

  small {
    display: block;
    margin-top: 0.25rem;
    color: #666;
    font-size: 0.8rem;
  }

  .form-actions {
    display: flex;
    gap: 1rem;
  }

  @media (max-width: 768px) {
    .edit-form-container {
      grid-template-columns: 1fr;
    }
  }
</style>
