<script>
	import { goto } from '$app/navigation';
	import { api } from '$lib/utils/api';
	import logger from '$lib/client/logger';
	import ImageSelector from '$lib/components/admin/ImageSelector.svelte';

	// Create a logger with context
	const log = logger.withContext('news-edit');

	// Props from page data
	export let data;

	// Form state - initialize with existing article data
	let article = {
		id: data.article.id,
		title: data.article.title,
		content: data.article.content,
		imageUrl: data.article.imageUrl || '',
		published: data.article.published
	};

	let loading = false;
	/** @type {string|null} */
	let error = null;
	/** @type {string|null} */
	let successMessage = null;

	/**
	 * Handle image selection from ImageSelector
	 */
	function handleImageSelect(event) {
		const { imageUrl } = event.detail;
		article.imageUrl = imageUrl;
	}

	/**
	 * Update the news article
	 */
	async function updateArticle() {
		if (!article.title || !article.content) {
			error = 'Title and content are required';
			return;
		}

		loading = true;
		error = null;
		successMessage = null;

		log.info('Updating news article', { id: article.id, title: article.title });
		const { success, data: updatedData, error: apiError } = await api.put(`/api/news/${article.id}`, {
			title: article.title,
			content: article.content,
			imageUrl: article.imageUrl,
			published: article.published
		});

		if (success && updatedData) {
			log.info('News article updated successfully', updatedData);
			successMessage = 'Article updated successfully!';
			// Update local article data
			article = { ...article, ...updatedData };
		} else {
			log.error('Failed to update news article', apiError);
			error = apiError || 'Failed to update news article';
		}

		loading = false;
	}

	/**
	 * Handle content change for rich text editor
	 */
	function handleContentChange(event) {
		article.content = event.target.value;
	}
</script>

<svelte:head>
	<title>Edit News Article - Admin</title>
</svelte:head>

<div class="edit-news-container">
	<div class="header">
		<h1>Edit News Article</h1>
		<a href="/admin/news" class="btn secondary">← Back to News</a>
	</div>

	{#if error}
		<div class="error-message">
			<p>{error}</p>
			<button class="btn secondary" on:click={() => error = null}>Dismiss</button>
		</div>
	{/if}

	{#if successMessage}
		<div class="success-message">
			<p>{successMessage}</p>
			<button class="btn secondary" on:click={() => successMessage = null}>Dismiss</button>
		</div>
	{/if}

	<form on:submit|preventDefault={updateArticle} class="article-form">
		<div class="form-group">
			<label for="title">Title *</label>
			<input
				type="text"
				id="title"
				bind:value={article.title}
				required
				placeholder="Enter article title"
				disabled={loading}
			/>
		</div>

		<div class="form-group">
			<label>Featured Image</label>
			<ImageSelector
				selectedImageUrl={article.imageUrl}
				on:select={handleImageSelect}
				disabled={loading}
			/>
		</div>

		<div class="form-group">
			<label for="content">Content *</label>
			<textarea
				id="content"
				bind:value={article.content}
				required
				placeholder="Write your article content here..."
				rows="15"
				disabled={loading}
			></textarea>
			<p class="help-text">
				You can use basic HTML tags for formatting: &lt;p&gt;, &lt;strong&gt;, &lt;em&gt;, &lt;br&gt;, &lt;a&gt;, etc.
			</p>
		</div>

		<div class="form-group checkbox">
			<label>
				<input
					type="checkbox"
					bind:checked={article.published}
					disabled={loading}
				/>
				Published
			</label>
		</div>

		<div class="form-actions">
			<button type="submit" class="btn primary" disabled={loading}>
				{loading ? 'Updating...' : 'Update Article'}
			</button>
			<a href="/admin/news" class="btn secondary">Cancel</a>
		</div>
	</form>
</div>

<style>
	.edit-news-container {
		max-width: 800px;
		margin: 0 auto;
		padding: 2rem;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 2rem;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		text-decoration: none;
		display: inline-flex;
		align-items: center;
		justify-content: center;
	}

	.primary {
		background-color: #4caf50;
		color: white;
	}

	.secondary {
		background-color: #f0f0f0;
		color: #333;
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.error-message {
		background-color: #ffebee;
		color: #c62828;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.success-message {
		background-color: #e8f5e8;
		color: #2e7d32;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.article-form {
		background-color: #f9f9f9;
		padding: 2rem;
		border-radius: 8px;
	}

	.form-group {
		margin-bottom: 1.5rem;
	}

	.form-group label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: bold;
	}

	.form-group input,
	.form-group textarea {
		width: 100%;
		padding: 0.75rem;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 1rem;
		font-family: inherit;
	}

	.form-group textarea {
		resize: vertical;
		min-height: 300px;
	}

	.form-group.checkbox label {
		display: flex;
		align-items: center;
		font-weight: normal;
	}

	.form-group.checkbox input {
		width: auto;
		margin-right: 0.5rem;
	}

	.help-text {
		font-size: 0.9rem;
		color: #666;
		margin-top: 0.5rem;
		margin-bottom: 0;
	}

	.form-actions {
		display: flex;
		gap: 1rem;
		margin-top: 2rem;
	}
</style>
