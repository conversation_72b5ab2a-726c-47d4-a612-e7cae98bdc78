<script>
	import { theme } from '$lib/stores/theme';
	import { onMount } from 'svelte';

	// Current theme value
	$: currentTheme = $theme;

	// Initialize theme on mount
	onMount(() => {
		theme.init();
	});

	/**
	 * Handle theme toggle
	 */
	function handleToggle() {
		theme.toggle();
	}
</script>

<button 
	class="theme-toggle" 
	on:click={handleToggle}
	title={currentTheme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}
	aria-label={currentTheme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}
>
	<div class="toggle-container">
		<!-- Sun icon for light mode -->
		<svg 
			class="icon sun-icon" 
			class:active={currentTheme === 'light'}
			width="20" 
			height="20" 
			viewBox="0 0 24 24" 
			fill="none" 
			stroke="currentColor" 
			stroke-width="2"
		>
			<circle cx="12" cy="12" r="5"/>
			<line x1="12" y1="1" x2="12" y2="3"/>
			<line x1="12" y1="21" x2="12" y2="23"/>
			<line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
			<line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
			<line x1="1" y1="12" x2="3" y2="12"/>
			<line x1="21" y1="12" x2="23" y2="12"/>
			<line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
			<line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
		</svg>
		
		<!-- Moon icon for dark mode -->
		<svg 
			class="icon moon-icon" 
			class:active={currentTheme === 'dark'}
			width="20" 
			height="20" 
			viewBox="0 0 24 24" 
			fill="none" 
			stroke="currentColor" 
			stroke-width="2"
		>
			<path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
		</svg>
	</div>
	
	<span class="toggle-text">
		{currentTheme === 'light' ? 'Dark' : 'Light'}
	</span>
</button>

<style>
	.theme-toggle {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		background: var(--theme-button-bg);
		color: var(--theme-button-text);
		border: 1px solid var(--theme-border);
		border-radius: 6px;
		padding: 0.5rem 0.75rem;
		cursor: pointer;
		font-size: 0.9rem;
		font-weight: 500;
		transition: all 0.2s ease;
		position: relative;
		overflow: hidden;
	}

	.theme-toggle:hover {
		background: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
		transform: translateY(-1px);
		box-shadow: 0 2px 8px var(--theme-shadow-hover);
	}

	.theme-toggle:active {
		transform: translateY(0);
	}

	.toggle-container {
		position: relative;
		width: 20px;
		height: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.icon {
		position: absolute;
		transition: all 0.3s ease;
		opacity: 0;
		transform: scale(0.8) rotate(180deg);
	}

	.icon.active {
		opacity: 1;
		transform: scale(1) rotate(0deg);
	}

	.sun-icon.active {
		color: #ffa500;
	}

	.moon-icon.active {
		color: #4a90e2;
	}

	.toggle-text {
		font-size: 0.85rem;
		font-weight: 500;
		letter-spacing: 0.025em;
	}

	/* Responsive design */
	@media (max-width: 768px) {
		.theme-toggle {
			padding: 0.4rem 0.6rem;
			font-size: 0.8rem;
		}
		
		.toggle-text {
			display: none;
		}
	}

	/* Focus styles for accessibility */
	.theme-toggle:focus {
		outline: 2px solid var(--theme-accent-primary);
		outline-offset: 2px;
	}

	/* High contrast mode support */
	@media (prefers-contrast: high) {
		.theme-toggle {
			border-width: 2px;
		}
	}

	/* Reduced motion support */
	@media (prefers-reduced-motion: reduce) {
		.theme-toggle,
		.icon {
			transition: none;
		}
	}
</style>
