<script>
	import { goto } from '$app/navigation';
	import { api } from '$lib/utils/api';
	import logger from '$lib/client/logger';
	import ImageSelector from '$lib/components/admin/ImageSelector.svelte';

	// Create a logger with context
	const log = logger.withContext('news-create');

	// Form state
	let article = {
		title: '',
		content: '',
		imageUrl: '',
		published: false
	};

	let loading = false;
	/** @type {string|null} */
	let error = null;

	/**
	 * Handle image selection from ImageSelector
	 */
	function handleImageSelect(event) {
		const { imageUrl } = event.detail;
		article.imageUrl = imageUrl;
	}

	/**
	 * Create the news article
	 */
	async function createArticle() {
		if (!article.title || !article.content) {
			error = 'Title and content are required';
			return;
		}

		loading = true;
		error = null;

		log.info('Creating new news article', { title: article.title });
		const { success, data, error: apiError } = await api.post('/api/news', article);

		if (success && data) {
			log.info('News article created successfully', data);
			// Redirect to the news management page
			goto('/admin/news');
		} else {
			log.error('Failed to create news article', apiError);
			error = apiError || 'Failed to create news article';
			loading = false;
		}
	}

	/**
	 * Handle content change for rich text editor
	 */
	function handleContentChange(event) {
		article.content = event.target.value;
	}
</script>

<svelte:head>
	<title>Create News Article - Admin</title>
</svelte:head>

<div class="create-news-container">
	<div class="header">
		<h1>Create News Article</h1>
		<a href="/admin/news" class="btn secondary">← Back to News</a>
	</div>

	{#if error}
		<div class="error-message">
			<p>{error}</p>
			<button class="btn secondary" on:click={() => error = null}>Dismiss</button>
		</div>
	{/if}

	<form on:submit|preventDefault={createArticle} class="article-form">
		<div class="form-group">
			<label for="title">Title *</label>
			<input
				type="text"
				id="title"
				bind:value={article.title}
				required
				placeholder="Enter article title"
				disabled={loading}
			/>
		</div>

		<div class="form-group">
			<label>Featured Image</label>
			<ImageSelector
				selectedImageUrl={article.imageUrl}
				on:select={handleImageSelect}
				disabled={loading}
			/>
		</div>

		<div class="form-group">
			<label for="content">Content *</label>
			<textarea
				id="content"
				bind:value={article.content}
				required
				placeholder="Write your article content here..."
				rows="15"
				disabled={loading}
			></textarea>
			<p class="help-text">
				You can use basic HTML tags for formatting: &lt;p&gt;, &lt;strong&gt;, &lt;em&gt;, &lt;br&gt;, &lt;a&gt;, etc.
			</p>
		</div>

		<div class="form-group checkbox">
			<label>
				<input
					type="checkbox"
					bind:checked={article.published}
					disabled={loading}
				/>
				Publish immediately
			</label>
		</div>

		<div class="form-actions">
			<button type="submit" class="btn primary" disabled={loading}>
				{loading ? 'Creating...' : 'Create Article'}
			</button>
			<a href="/admin/news" class="btn secondary">Cancel</a>
		</div>
	</form>
</div>

<style>
	.create-news-container {
		max-width: 800px;
		margin: 0 auto;
		padding: 2rem;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 2rem;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		text-decoration: none;
		display: inline-flex;
		align-items: center;
		justify-content: center;
	}

	.primary {
		background-color: #4caf50;
		color: white;
	}

	.secondary {
		background-color: #f0f0f0;
		color: #333;
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.error-message {
		background-color: #ffebee;
		color: #c62828;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.article-form {
		background-color: #f9f9f9;
		padding: 2rem;
		border-radius: 8px;
	}

	.form-group {
		margin-bottom: 1.5rem;
	}

	.form-group label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: bold;
	}

	.form-group input,
	.form-group textarea {
		width: 100%;
		padding: 0.75rem;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 1rem;
		font-family: inherit;
	}

	.form-group textarea {
		resize: vertical;
		min-height: 300px;
	}

	.form-group.checkbox label {
		display: flex;
		align-items: center;
		font-weight: normal;
	}

	.form-group.checkbox input {
		width: auto;
		margin-right: 0.5rem;
	}

	.help-text {
		font-size: 0.9rem;
		color: #666;
		margin-top: 0.5rem;
		margin-bottom: 0;
	}

	.form-actions {
		display: flex;
		gap: 1rem;
		margin-top: 2rem;
	}
</style>
