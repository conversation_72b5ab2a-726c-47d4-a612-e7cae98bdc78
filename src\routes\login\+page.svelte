<script>
	import { goto } from '$app/navigation';

	// Form state
	let email = '';
	let password = '';
	let rememberMe = false;
	let errorMessage = '';
	let isLoading = false;

	// Handle form submission
	async function handleSubmit() {
		// Reset error message
		errorMessage = '';

		// Validate form
		if (!email || !password) {
			errorMessage = 'Please enter both email and password.';
			return;
		}

		// Show loading state
		isLoading = true;

		try {
			// Send login request to the API
			const response = await fetch('/api/auth/login', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					email,
					password
				})
			});

			const data = await response.json();

			if (response.ok && data.success) {
				// Check if user is admin
				if (data.data.role === 'admin') {
					// Redirect to admin dashboard
					goto('/admin');
				} else {
					// Redirect to home page for regular users
					goto('/');
				}
			} else {
				// Show error message
				errorMessage = data.error || 'Invalid email or password. Please try again.';
				isLoading = false;
			}
		} catch (error) {
			console.error('Login error:', error);
			errorMessage = 'An error occurred during login. Please try again.';
			isLoading = false;
		}
	}
</script>

<svelte:head>
	<title>Login - Finn Wolfhard Fan Club</title>
	<meta name="description" content="Log in to your Finn Wolfhard Fan Club account" />
</svelte:head>

<div class="login-container">
	<div class="login-card">
		<h1>Log In</h1>

		{#if errorMessage}
			<div class="error-message">
				{errorMessage}
			</div>
		{/if}

		<form on:submit|preventDefault={handleSubmit}>
			<div class="form-group">
				<label for="email">Email</label>
				<input
					type="email"
					id="email"
					bind:value={email}
					placeholder="Enter your email"
					disabled={isLoading}
				/>
			</div>

			<div class="form-group">
				<label for="password">Password</label>
				<input
					type="password"
					id="password"
					bind:value={password}
					placeholder="Enter your password"
					disabled={isLoading}
				/>
			</div>

			<div class="form-options">
				<div class="remember-me">
					<input
						type="checkbox"
						id="remember-me"
						bind:checked={rememberMe}
						disabled={isLoading}
					/>
					<label for="remember-me">Remember me</label>
				</div>

				<a href="/forgot-password" class="forgot-password">Forgot password?</a>
			</div>

			<button type="submit" class="btn primary" disabled={isLoading}>
				{#if isLoading}
					Logging in...
				{:else}
					Log In
				{/if}
			</button>
		</form>

		<div class="register-prompt">
			<p>Don't have an account? <a href="/register">Register</a></p>
		</div>

		<div class="demo-credentials">
			<p>For admin access, use:</p>
			<p>Email: <strong><EMAIL></strong></p>
			<p>Password: <strong>admin</strong></p>
			<p class="demo-note">For regular user access:</p>
			<p>Email: <strong><EMAIL></strong></p>
			<p>Password: <strong>password</strong></p>
		</div>
	</div>

	<div class="accessibility-note">
		<h2>Accessibility Features</h2>
		<p>Our website is designed to be accessible to all users. You can customize your experience using the accessibility controls in the footer.</p>
		<ul>
			<li>High Contrast Mode: Enhances visibility with stronger color contrasts</li>
			<li>Large Text Mode: Increases text size throughout the site</li>
			<li>Simplified Interface: Reduces visual complexity for easier navigation</li>
		</ul>
		<p>Need assistance? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
	</div>
</div>

<style>
	.login-container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 2rem 1rem;
		display: flex;
		flex-direction: column;
		gap: 2rem;
	}

	.login-card {
		background-color: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		border-radius: 8px;
		box-shadow: 0 4px 6px var(--theme-shadow);
		padding: 2rem;
		color: var(--theme-text-primary);
	}

	h1 {
		text-align: center;
		margin-bottom: 1.5rem;
	}

	.error-message {
		background-color: var(--theme-accent-danger);
		color: white;
		padding: 0.8rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		text-align: center;
		border: 1px solid var(--theme-accent-danger);
	}

	.form-group {
		margin-bottom: 1.5rem;
	}

	label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: 600;
	}

	input[type="email"],
	input[type="password"] {
		width: 100%;
		padding: 0.8rem;
		border: 1px solid var(--theme-input-border);
		border-radius: 4px;
		font-size: 1rem;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
	}

	input[type="email"]:focus,
	input[type="password"]:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
	}

	.form-options {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 1.5rem;
	}

	.remember-me {
		display: flex;
		align-items: center;
	}

	.remember-me input {
		margin-right: 0.5rem;
	}

	.forgot-password {
		color: var(--theme-accent-primary);
		text-decoration: none;
	}

	.forgot-password:hover {
		color: var(--theme-accent-primary-hover);
		text-decoration: underline;
	}

	.btn {
		display: block;
		width: 100%;
		padding: 0.8rem;
		border: none;
		border-radius: 4px;
		font-weight: bold;
		cursor: pointer;
		transition: all 0.3s ease;
		font-size: 1rem;
	}

	.btn.primary {
		background-color: var(--theme-accent-primary);
		color: white;
		border: 1px solid var(--theme-accent-primary);
	}

	.btn.primary:hover:not(:disabled) {
		background-color: var(--theme-accent-primary-hover);
		border-color: var(--theme-accent-primary-hover);
	}

	.btn:disabled {
		opacity: 0.7;
		cursor: not-allowed;
	}

	.register-prompt {
		text-align: center;
		margin-top: 1.5rem;
		padding-top: 1.5rem;
		border-top: 1px solid var(--theme-border);
		color: var(--theme-text-primary);
	}

	.register-prompt a {
		color: var(--theme-accent-primary);
		font-weight: bold;
		text-decoration: none;
	}

	.register-prompt a:hover {
		color: var(--theme-accent-primary-hover);
		text-decoration: underline;
	}

	.demo-credentials {
		margin-top: 1.5rem;
		padding: 1rem;
		background-color: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		border-radius: 4px;
		font-size: 0.9rem;
		color: var(--theme-text-primary);
	}

	.demo-credentials p {
		margin: 0.3rem 0;
	}

	.demo-note {
		margin-top: 0.8rem !important;
		font-weight: bold;
		border-top: 1px solid var(--theme-border);
		padding-top: 0.8rem;
		color: var(--theme-text-primary);
	}

	.accessibility-note {
		background-color: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		padding: 1.5rem;
		border-radius: 8px;
		color: var(--theme-text-primary);
	}

	.accessibility-note h2 {
		margin-top: 0;
		margin-bottom: 1rem;
		font-size: 1.5rem;
		color: var(--theme-text-primary);
	}

	.accessibility-note ul {
		margin-bottom: 1rem;
	}

	.accessibility-note li {
		margin-bottom: 0.5rem;
		color: var(--theme-text-secondary);
	}

	.accessibility-note a {
		color: var(--theme-accent-primary);
		text-decoration: none;
	}

	.accessibility-note a:hover {
		color: var(--theme-accent-primary-hover);
		text-decoration: underline;
	}

	@media (min-width: 768px) {
		.login-container {
			flex-direction: row;
			align-items: flex-start;
		}

		.login-card {
			flex: 1;
		}

		.accessibility-note {
			flex: 1;
		}
	}
</style>
