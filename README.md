# FWFC - Finn <PERSON> Fan Club Website

A comprehensive fan club website built with SvelteKit, featuring news management, hero image management, user authentication, and a complete admin interface.

## 🚀 Features Overview

### ✅ **Hero Images System**
- Dynamic hero image display with database-driven content
- Admin interface for uploading and managing hero images
- Automatic image optimization and responsive display
- Gallery picker with search and filtering capabilities

### ✅ **News Management System**
- Complete CRUD operations for news articles
- Rich text editor with HTML formatting support
- Featured image selection for articles
- Published/draft status management
- Individual article pages with SEO optimization
- News listing page with search functionality
- Homepage integration with latest news display

### ✅ **Admin Interface**
- Secure authentication system
- Role-based access control
- Image gallery management
- News article management
- User-friendly admin dashboard

### ✅ **Technical Features**
- SQLite database with Drizzle ORM
- Server-side rendering (SSR)
- Responsive design for all devices
- RESTful API endpoints
- Type-safe development with TypeScript
- Component-based architecture

## 🛠 Quick Start

### Prerequisites
- Node.js 18+
- npm, pnpm, or yarn

### Installation

1. **Clone and install dependencies:**
```bash
git clone <repository-url>
cd fwfc2
npm install
```

2. **Set up the database:**
```bash
npm run db:generate
npm run db:migrate
npm run db:seed
```

3. **Start development server:**
```bash
npm run dev
# or open in browser automatically
npm run dev -- --open
```

4. **Access the application:**
- **Website**: http://localhost:5173
- **Admin Panel**: http://localhost:5173/admin
- **Default Admin Credentials**:
  - Email: `<EMAIL>`
  - Password: `admin`

## 📖 Detailed Documentation

### Hero Images System

The hero images system provides dynamic homepage banners managed through the admin interface.

#### Database Schema
```sql
CREATE TABLE hero_images (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  subtitle TEXT,
  image_url TEXT NOT NULL,
  active BOOLEAN DEFAULT false,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);
```

#### API Endpoints
- `GET /api/hero-images` - Get active hero image
- `POST /api/hero-images` - Create new hero image (admin only)
- `PUT /api/hero-images/:id` - Update hero image (admin only)
- `DELETE /api/hero-images/:id` - Delete hero image (admin only)

#### Admin Management
1. Navigate to **Admin Panel** → **Hero Images**
2. Upload new images or select from gallery
3. Set title and subtitle text
4. Activate/deactivate hero images
5. Changes appear immediately on homepage

### News Management System

A complete content management system for news articles with rich text editing capabilities.

#### Database Schema
```sql
CREATE TABLE news (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  image_url TEXT,
  author_id INTEGER,
  published BOOLEAN DEFAULT false,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (author_id) REFERENCES users (id)
);
```

#### API Endpoints
- `GET /api/news` - Get published news articles (public)
- `GET /api/news?all=true` - Get all articles (admin only)
- `POST /api/news` - Create new article (admin only)
- `PUT /api/news/:id` - Update article (admin only)
- `DELETE /api/news/:id` - Delete article (admin only)

#### Rich Text Editor
The news system includes a sophisticated rich text editor with:

**Primary Editor (TinyMCE):**
- Full WYSIWYG editing experience
- Advanced formatting options
- Image insertion and management
- Table creation and editing
- Link management

**Fallback Editor (Custom):**
- Reliable contenteditable-based editor
- Basic formatting toolbar (Bold, Italic, Underline, Lists, Links)
- Automatic fallback if TinyMCE fails to load
- Clean HTML output

#### Featured Images
- **Gallery Integration**: Select from existing uploaded images
- **Direct Upload**: Upload new images directly from the editor
- **Image Preview**: Real-time preview of selected images
- **Responsive Display**: Automatic optimization for different screen sizes

#### Article Management Workflow

**Creating Articles:**
1. Navigate to **Admin Panel** → **News** → **Create New Article**
2. Enter article title
3. Select featured image (optional)
4. Write content using the rich text editor
5. Set published status (Draft/Published)
6. Save article

**Editing Articles:**
1. Go to **Admin Panel** → **News**
2. Click edit button (✏️) on any article
3. Modify content, images, or status
4. Save changes

**Publishing Workflow:**
- **Draft**: Article visible only in admin panel
- **Published**: Article visible on public website
- **Toggle Status**: Quick publish/unpublish from article listing

#### Public Display

**Homepage Integration:**
- Latest 3 published articles displayed in "Latest News" section
- Article previews with title, excerpt, and featured image
- "Read More" links to individual article pages

**Individual Article Pages (`/news/[id]`):**
- Clean, readable article layout
- Full HTML content rendering
- Featured image display
- Publication and update dates
- SEO-optimized meta tags
- Social media sharing tags (Open Graph)
- Navigation back to homepage

**News Listing Page (`/news`):**
- All published articles in grid layout
- Client-side search functionality
- Article previews with excerpts
- Pagination support (ready for large numbers of articles)
- Responsive design for all devices

## 🏗 Technical Implementation

### Architecture Overview

**Frontend:**
- **SvelteKit** - Full-stack framework with SSR
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and dev server
- **Component-based architecture** - Reusable UI components

**Backend:**
- **SvelteKit API Routes** - Server-side API endpoints
- **Drizzle ORM** - Type-safe database operations
- **SQLite** - Lightweight database for development
- **Session-based authentication** - Secure user sessions

### Database Schema

The application uses SQLite with the following main tables:

```sql
-- Users table
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT UNIQUE NOT NULL,
  email TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  role TEXT DEFAULT 'user',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Hero Images table
CREATE TABLE hero_images (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  subtitle TEXT,
  image_url TEXT NOT NULL,
  active BOOLEAN DEFAULT false,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- News table
CREATE TABLE news (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  image_url TEXT,
  author_id INTEGER,
  published BOOLEAN DEFAULT false,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (author_id) REFERENCES users (id)
);

-- Gallery Images table
CREATE TABLE gallery_images (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  filename TEXT NOT NULL,
  original_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  mime_type TEXT NOT NULL,
  uploaded_by INTEGER,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (uploaded_by) REFERENCES users (id)
);
```

### File Structure

```
src/
├── lib/
│   ├── components/
│   │   ├── admin/
│   │   │   ├── ImageSelector.svelte      # Image selection component
│   │   │   ├── RichTextEditor.svelte     # Rich text editor
│   │   │   ├── GalleryPicker.svelte      # Image gallery picker
│   │   │   └── ImageUploader.svelte      # Image upload component
│   │   └── ui/                           # General UI components
│   ├── server/
│   │   ├── db/
│   │   │   ├── schema.ts                 # Database schema definitions
│   │   │   └── index.ts                  # Database connection
│   │   └── auth.ts                       # Authentication utilities
│   ├── utils/
│   │   ├── api.ts                        # API client utilities
│   │   └── validation.ts                 # Form validation
│   └── client/
│       └── logger.ts                     # Client-side logging
├── routes/
│   ├── admin/
│   │   ├── +layout.svelte                # Admin layout
│   │   ├── +page.svelte                  # Admin dashboard
│   │   ├── hero-images/                  # Hero image management
│   │   └── news/                         # News management
│   │       ├── +page.svelte              # News listing
│   │       ├── create/                   # Create article
│   │       └── edit/[id]/                # Edit article
│   ├── news/
│   │   ├── +page.svelte                  # News listing page
│   │   ├── +page.server.ts               # Server-side data loading
│   │   └── [id]/                         # Individual article pages
│   │       ├── +page.svelte              # Article display
│   │       └── +page.server.ts           # Article data loading
│   ├── api/
│   │   ├── auth/                         # Authentication endpoints
│   │   ├── hero-images/                  # Hero image API
│   │   ├── news/                         # News API
│   │   └── gallery/                      # Image gallery API
│   ├── login/                            # Login page
│   └── +page.svelte                      # Homepage
└── static/
    └── images/                           # Static image assets
```

### Component Architecture

**ImageSelector Component:**
- Unified interface for image selection
- Supports both gallery picking and direct upload
- Real-time preview functionality
- Used across hero images and news articles

**RichTextEditor Component:**
- Primary: TinyMCE with full WYSIWYG features
- Fallback: Custom contenteditable with basic formatting
- Automatic fallback mechanism for reliability
- Clean HTML output for consistent rendering

**Authentication System:**
- Session-based authentication
- Role-based access control (admin/user)
- Secure password hashing
- Protected API endpoints

## 📋 Usage Instructions

### Admin Panel Access

1. **Login to Admin Panel:**
   ```
   URL: http://localhost:5173/login
   Email: <EMAIL>
   Password: admin
   ```

2. **Navigate Admin Interface:**
   - **Dashboard**: Overview of system status
   - **Hero Images**: Manage homepage banners
   - **News**: Create and manage articles
   - **Gallery**: View uploaded images

### Managing Hero Images

1. **Access Hero Images:**
   - Go to **Admin Panel** → **Hero Images**

2. **Create New Hero Image:**
   - Click **"Create New Hero Image"**
   - Enter title and subtitle
   - Select or upload background image
   - Click **"Create Hero Image"**

3. **Activate Hero Image:**
   - Click the **"Activate"** button on desired image
   - Only one hero image can be active at a time
   - Changes appear immediately on homepage

### Managing News Articles

1. **Create New Article:**
   - Go to **Admin Panel** → **News** → **"Create New Article"**
   - Enter article title
   - Select featured image (optional)
   - Write content using rich text editor
   - Choose published status
   - Click **"Create Article"**

2. **Edit Existing Article:**
   - Go to **Admin Panel** → **News**
   - Click edit button (✏️) on any article
   - Make changes and save

3. **Publish/Unpublish Articles:**
   - **Method 1**: Toggle eye icon (👁️) in article listing
   - **Method 2**: Edit article and change published status

4. **Delete Articles:**
   - Click delete button (🗑️) in article listing
   - Confirm deletion in dialog

### Rich Text Editor Usage

**Formatting Options:**
- **Bold**: Select text and click **B** or use Ctrl+B
- **Italic**: Select text and click **I** or use Ctrl+I
- **Lists**: Click bullet or numbered list buttons
- **Links**: Select text, click link button, enter URL

**Image Insertion:**
- Click image button in toolbar
- Select from gallery or upload new image
- Images are automatically optimized

### Public Website Features

**Homepage (`/`):**
- Dynamic hero image with title/subtitle
- Latest 3 published news articles
- "View All News" link to full listing

**News Listing (`/news`):**
- All published articles in grid layout
- Search functionality (type in search box)
- Click any article to read full content

**Individual Articles (`/news/[id]`):**
- Full article content with HTML formatting
- Featured image display
- Publication date
- "Back to Home" navigation

## ✅ Complete Features Summary

### 🎯 **Working Features**

| Feature | Status | Description |
|---------|--------|-------------|
| **Hero Images** | ✅ **Complete** | Dynamic homepage banners with admin management |
| **News Management** | ✅ **Complete** | Full CRUD operations with rich text editor |
| **Individual Article Pages** | ✅ **Complete** | SEO-optimized article display with HTML rendering |
| **News Listing** | ✅ **Complete** | Searchable article listing with pagination support |
| **Rich Text Editor** | ✅ **Complete** | TinyMCE with reliable fallback editor |
| **Image Management** | ✅ **Complete** | Gallery picker and direct upload functionality |
| **Admin Authentication** | ✅ **Complete** | Secure login with role-based access |
| **Admin Interface** | ✅ **Complete** | User-friendly admin dashboard |
| **Database Integration** | ✅ **Complete** | SQLite with Drizzle ORM |
| **API Endpoints** | ✅ **Complete** | RESTful APIs for all features |
| **Responsive Design** | ✅ **Complete** | Mobile-friendly interface |
| **SEO Optimization** | ✅ **Complete** | Meta tags and Open Graph support |

### 🔧 **Technical Capabilities**

- **Server-Side Rendering (SSR)** - Fast initial page loads
- **Type Safety** - Full TypeScript implementation
- **Component Architecture** - Reusable Svelte components
- **Database Migrations** - Version-controlled schema changes
- **Image Optimization** - Automatic image processing
- **Error Handling** - Comprehensive error management
- **Security** - Protected admin routes and API endpoints
- **Performance** - Optimized for speed and efficiency

### 🚀 **Production Ready Features**

- **Content Management** - Complete CMS for news articles
- **Media Management** - Image upload and gallery system
- **User Management** - Admin authentication and authorization
- **Public Website** - Fully functional fan club website
- **Admin Dashboard** - Comprehensive admin interface
- **API Documentation** - Well-documented API endpoints

## 🛠 Development

### Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run dev -- --open   # Start dev server and open browser

# Database
npm run db:generate      # Generate database migrations
npm run db:migrate       # Run database migrations
npm run db:seed          # Seed database with sample data
npm run db:studio        # Open Drizzle Studio (database GUI)

# Building
npm run build            # Build for production
npm run preview          # Preview production build

# Code Quality
npm run lint             # Run ESLint
npm run format           # Format code with Prettier
npm run type-check       # Run TypeScript type checking
```

### Environment Setup

1. **Clone Repository:**
```bash
git clone <repository-url>
cd fwfc2
```

2. **Install Dependencies:**
```bash
npm install
```

3. **Set up Database:**
```bash
npm run db:generate
npm run db:migrate
npm run db:seed
```

4. **Start Development:**
```bash
npm run dev
```

### Database Management

**View Database:**
```bash
npm run db:studio
```

**Reset Database:**
```bash
rm local.db
npm run db:migrate
npm run db:seed
```

**Create New Migration:**
```bash
npm run db:generate
```

### Production Deployment

1. **Build Application:**
```bash
npm run build
```

2. **Set Environment Variables:**
```bash
# Set production database URL
DATABASE_URL=your_production_database_url

# Set session secret
SESSION_SECRET=your_secure_session_secret
```

3. **Deploy:**
- The built application is in the `build/` directory
- Deploy to your preferred hosting platform
- Ensure database is set up and migrated

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **SvelteKit** - Full-stack framework
- **Drizzle ORM** - Type-safe database operations
- **TinyMCE** - Rich text editing
- **Vite** - Fast build tool

---

**Built with ❤️ for the Finn Wolfhard Fan Community**
