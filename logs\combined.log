{"level":"debug","message":"No session cookie found","timestamp":"2025-05-16 11:10:50:1050"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-16 11:10:50:1050"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-16 11:11:03:113"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-16 11:11:03:113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:08:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:08:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:10:1110"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:13:1113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:14:1114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:47:1447"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:48:1448"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:48:1448"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:48:1448"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:49:1449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:49:1449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:49:1449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:49:1449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:50:1450"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:50:1450"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:51:1451"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:00:150"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:05:155"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-16 11:15:05:155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:05:155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:08:158"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:08:158"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:12:1512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:12:1512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:12:1512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:12:1512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:14:1514"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:15:1515"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:17:1517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:17:1517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:18:1518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:20:1520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:20:1520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:21:1521"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:22:1522"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:41:1641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:41:1641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:43:1643"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:44:1644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:44:1644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:44:1644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:51:1651"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-16 11:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:54:1654"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:57:1657"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:01:171"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:04:174"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:04:174"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:06:176"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:06:176"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:06:176"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:06:176"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:07:177"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:20:30:2030"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:20:52:2052"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:20:57:2057"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:04:214"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:12:2112"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-16 11:21:12:2112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:12:2112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:14:2114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:16:2116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:18:2118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:18:2118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:18:2118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:20:2120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:20:2120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:22:2122"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:23:2123"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:24:2124"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:41:2141"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:52:2152"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:52:2152"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:54:2154"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:54:2154"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:58:2158"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:31:2231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:31:2231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:38:2238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:38:2238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:38:2238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:40:2240"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:40:2240"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:41:2241"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:41:2241"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:42:2242"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:43:2243"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:44:2244"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:44:2244"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:46:2246"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:46:2246"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:47:2247"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:48:2248"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:55:2255"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:55:2255"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:57:2257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:57:2257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:00:230"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:01:231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:01:231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:18:2318"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:20:2320"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:50:2350"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:51:2351"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:51:2351"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:53:2353"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:54:2354"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:54:2354"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:55:2355"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:07:247"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:07:247"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:09:249"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:09:249"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:28:2428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:28:2428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:33:2433"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:34:2434"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:35:2435"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:25:18:2518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:25:37:2537"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:25:38:2538"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-29 14:31:58:3158"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-29 14:32:06:326"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-29 14:33:18:3318"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-29 14:33:18:3318"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:33:18:3318"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:33:22:3322"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:51:22:5122"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:51:25:5125"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:56:07:567"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:22:322"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:29:329"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:44:344"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:48:348"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:49:349"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:53:353"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:05:43:543"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:05:45:545"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:06:51:651"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:06:54:654"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:01:71"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:03:73"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:05:75"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:39:739"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:39:739"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:41:741"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:43:743"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:43:743"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:45:745"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:50:750"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:50:750"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:51:751"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:53:753"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:53:753"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:02:102"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:05:105"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:14:1014"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:47:1047"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:47:1047"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:49:1049"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:49:1049"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:11:08:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:11:23:1123"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:11:29:1129"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:27:1427"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:28:1428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:28:1428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:38:1438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:38:1438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:38:1438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:52:1452"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:52:1452"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:52:1452"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:15:06:156"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:15:06:156"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:15:06:156"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:06:166"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:06:166"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:07:167"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:58:1658"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:58:1658"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:59:1659"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:59:1659"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:00:170"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:03:173"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:03:173"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:03:173"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:12:1712"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:18:1718"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:18:57:1857"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:03:193"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:03:193"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:06:196"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:06:196"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:06:196"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:11:1911"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:29:1929"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:32:1932"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:32:1932"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:33:1933"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:34:1934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:35:1935"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:35:1935"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:36:1936"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:37:1937"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:37:1937"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:38:1938"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:39:1939"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:39:1939"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:42:1942"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:42:1942"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:42:1942"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:43:1943"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:44:1944"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:47:1947"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:48:1948"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:49:1949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:49:1949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:50:1950"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:53:1953"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:56:1956"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:59:1959"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:01:201"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:04:204"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:07:207"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:09:209"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:28:2028"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:30:2030"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:34:2034"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:12:2412"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:22:2422"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:34:2434"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:46:2446"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:58:2458"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:25:33:2533"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:25:38:2538"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:06:266"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:12:2612"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:27:2627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:28:2628"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:34:2634"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:41:2641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:41:2641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:41:2641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:44:2644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:01:271"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:02:272"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:03:273"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:03:273"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:03:273"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:11:2711"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:13:2713"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:13:2713"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:13:2713"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:15:2715"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:19:3119"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:27:3127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:27:3127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:27:3127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:28:3128"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:31:3131"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:32:3132"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:32:3132"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:35:3135"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:39:3139"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:40:3140"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:40:3140"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:18:3318"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:21:3321"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:22:3322"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:22:3322"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:37:3337"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:42:3342"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:45:3345"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:45:3345"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:45:3345"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:34:29:3429"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:34:52:3452"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:23:3523"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:23:3523"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:23:3523"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:58:3558"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:58:3558"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:58:3558"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:14:3614"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:14:3614"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:14:3614"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:28:3628"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:31:3631"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:32:3632"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:32:3632"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:32:3632"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:19:3819"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:22:3822"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:22:3822"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:22:3822"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:40:3840"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:41:3841"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:41:3841"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:41:3841"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:42:3842"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:44:3844"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:44:3844"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:44:3844"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:48:3848"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:49:3849"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:49:3849"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:49:3849"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:39:50:3950"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:39:55:3955"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:26:4026"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:26:4026"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:27:4027"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:27:4027"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:27:4027"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:27:4027"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:48:4048"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:41:39:4139"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:41:52:4152"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:41:56:4156"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:41:56:4156"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:41:56:4156"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:42:16:4216"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:42:30:4230"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-03 16:42:30:4230"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:16:4316"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:16:4316"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:16:4316"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:17:4317"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:17:4317"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:38:4338"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:38:4338"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:45:10:4510"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:46:27:4627"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-03 16:46:27:4627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:27:4627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:32:4632"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:32:4632"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:33:4633"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:37:4637"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:37:4637"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:48:50:4850"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:48:56:4856"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:48:57:4857"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:11:4911"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:13:4913"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:14:4914"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:36:4936"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:37:4937"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:38:4938"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:52:01:521"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:52:06:526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:52:07:527"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:53:46:5346"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:55:37:5537"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:55:46:5546"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:55:46:5546"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:44:5844"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:44:5844"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:44:5844"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:49:5849"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:53:5853"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:53:5853"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:53:5853"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:59:07:597"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:59:23:5923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:59:23:5923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:59:23:5923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:20:020"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:20:020"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:20:020"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:21:021"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:22:022"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:22:022"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:23:023"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:23:023"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:24:024"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:24:024"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:24:024"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:27:027"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:27:027"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:27:027"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:29:029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:29:029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:29:029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:15:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:15:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:15:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:16:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:16:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:17:117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:17:117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:18:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:21:121"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:21:121"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:21:121"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:31:231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:31:231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:32:232"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:32:232"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:32:232"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:33:233"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:33:233"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:34:234"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:34:234"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:35:235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:35:235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:35:235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:38:238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:38:238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:38:238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:08:58"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:08:58"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:09:59"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:09:59"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:09:59"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:10:510"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:10:510"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:11:511"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:46:546"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:54:554"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:06:07:67"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:03:133"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:08:138"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:08:138"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:08:138"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:18:1318"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:19:1319"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:20:1320"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:26:1726"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-03 17:17:26:1726"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:26:1726"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:28:1728"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:29:1729"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:29:1729"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:30:1730"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:31:1731"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:32:1732"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:32:1732"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:33:1733"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:42:1742"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:45:1745"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:46:1746"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:52:1752"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:18:09:189"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:18:12:1812"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:18:15:1815"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:18:15:1815"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:18:15:1815"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:19:38:1938"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:26:46:2646"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:12:3012"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:17:3017"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:29:3029"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-03 17:30:29:3029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:29:3029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:32:3032"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:33:3033"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:34:3034"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:39:3039"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:31:55:3155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:31:55:3155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:31:57:3157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:31:57:3157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:01:321"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:01:321"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:01:321"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:05:325"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:07:327"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:07:327"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:07:327"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:08:328"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:08:328"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:09:329"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:10:3210"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:11:3211"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:12:3212"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:12:3212"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:18:3218"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:22:3222"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:26:3226"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:27:3227"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:32:3232"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:35:3235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:38:3238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:43:3243"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:43:3243"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:43:3243"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:06:336"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:49:3349"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:52:3352"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:54:3354"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:55:3355"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:55:3355"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:58:3358"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:58:3358"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:36:10:3610"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:36:46:3646"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:20:3720"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:24:3724"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:26:3726"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:27:3727"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:59:3759"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:59:3759"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:59:3759"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:38:47:3847"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:38:51:3851"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:38:54:3854"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:25:3925"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:29:3929"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:34:3934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:34:3934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:34:3934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:40:3940"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:47:3947"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:49:3949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:59:3959"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:40:05:405"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:40:10:4010"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:40:11:4011"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:40:32:4032"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:40:58:4058"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:41:04:414"}
