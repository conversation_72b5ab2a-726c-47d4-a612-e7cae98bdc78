{"level":"debug","message":"No session cookie found","timestamp":"2025-05-16 11:10:50:1050"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-16 11:10:50:1050"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-16 11:11:03:113"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-16 11:11:03:113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:08:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:08:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:10:1110"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:13:1113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:14:1114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:47:1447"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:48:1448"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:48:1448"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:48:1448"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:49:1449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:49:1449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:49:1449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:49:1449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:50:1450"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:50:1450"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:51:1451"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:00:150"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:05:155"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-16 11:15:05:155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:05:155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:08:158"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:08:158"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:12:1512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:12:1512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:12:1512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:12:1512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:14:1514"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:15:1515"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:17:1517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:17:1517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:18:1518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:20:1520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:20:1520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:21:1521"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:22:1522"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:41:1641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:41:1641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:43:1643"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:44:1644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:44:1644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:44:1644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:51:1651"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-16 11:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:54:1654"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:57:1657"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:01:171"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:04:174"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:04:174"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:06:176"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:06:176"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:06:176"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:06:176"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:07:177"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:20:30:2030"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:20:52:2052"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:20:57:2057"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:04:214"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:12:2112"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-16 11:21:12:2112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:12:2112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:14:2114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:16:2116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:18:2118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:18:2118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:18:2118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:20:2120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:20:2120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:22:2122"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:23:2123"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:24:2124"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:41:2141"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:52:2152"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:52:2152"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:54:2154"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:54:2154"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:58:2158"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:31:2231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:31:2231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:38:2238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:38:2238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:38:2238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:40:2240"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:40:2240"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:41:2241"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:41:2241"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:42:2242"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:43:2243"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:44:2244"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:44:2244"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:46:2246"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:46:2246"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:47:2247"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:48:2248"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:55:2255"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:55:2255"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:57:2257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:57:2257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:00:230"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:01:231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:01:231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:18:2318"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:20:2320"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:50:2350"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:51:2351"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:51:2351"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:53:2353"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:54:2354"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:54:2354"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:55:2355"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:07:247"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:07:247"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:09:249"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:09:249"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:28:2428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:28:2428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:33:2433"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:34:2434"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:35:2435"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:25:18:2518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:25:37:2537"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:25:38:2538"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-29 14:31:58:3158"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-29 14:32:06:326"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-29 14:33:18:3318"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-29 14:33:18:3318"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:33:18:3318"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:33:22:3322"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:51:22:5122"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:51:25:5125"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:56:07:567"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:22:322"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:29:329"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:44:344"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:48:348"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:49:349"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:53:353"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:05:43:543"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:05:45:545"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:06:51:651"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:06:54:654"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:01:71"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:03:73"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:05:75"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:39:739"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:39:739"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:41:741"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:43:743"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:43:743"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:45:745"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:50:750"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:50:750"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:51:751"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:53:753"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:53:753"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:02:102"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:05:105"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:14:1014"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:47:1047"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:47:1047"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:49:1049"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:49:1049"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:11:08:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:11:23:1123"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:11:29:1129"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:27:1427"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:28:1428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:28:1428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:38:1438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:38:1438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:38:1438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:52:1452"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:52:1452"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:52:1452"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:15:06:156"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:15:06:156"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:15:06:156"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:06:166"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:06:166"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:07:167"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:58:1658"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:58:1658"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:59:1659"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:59:1659"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:00:170"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:03:173"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:03:173"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:03:173"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:12:1712"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:18:1718"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:18:57:1857"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:03:193"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:03:193"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:06:196"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:06:196"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:06:196"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:11:1911"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:29:1929"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:32:1932"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:32:1932"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:33:1933"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:34:1934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:35:1935"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:35:1935"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:36:1936"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:37:1937"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:37:1937"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:38:1938"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:39:1939"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:39:1939"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:42:1942"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:42:1942"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:42:1942"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:43:1943"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:44:1944"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:47:1947"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:48:1948"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:49:1949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:49:1949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:50:1950"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:53:1953"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:56:1956"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:59:1959"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:01:201"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:04:204"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:07:207"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:09:209"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:28:2028"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:30:2030"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:34:2034"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:12:2412"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:22:2422"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:34:2434"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:46:2446"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:58:2458"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:25:33:2533"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:25:38:2538"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:06:266"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:12:2612"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:27:2627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:28:2628"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:34:2634"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:41:2641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:41:2641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:41:2641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:44:2644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:01:271"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:02:272"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:03:273"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:03:273"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:03:273"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:11:2711"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:13:2713"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:13:2713"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:13:2713"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:15:2715"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:19:3119"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:27:3127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:27:3127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:27:3127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:28:3128"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:31:3131"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:32:3132"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:32:3132"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:35:3135"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:39:3139"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:40:3140"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:40:3140"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:18:3318"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:21:3321"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:22:3322"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:22:3322"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:37:3337"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:42:3342"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:45:3345"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:45:3345"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:45:3345"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:34:29:3429"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:34:52:3452"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:23:3523"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:23:3523"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:23:3523"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:58:3558"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:58:3558"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:58:3558"}
