<script>
	import { onMount } from 'svelte';
	import { api } from '$lib/utils/api';
	import logger from '$lib/client/logger';

	// Create a logger with context
	const log = logger.withContext('news-admin');

	// State variables
	/** @type {Array<{id: number, title: string, content: string, imageUrl?: string, authorId?: number, createdAt: string, updatedAt: string, published: boolean}>} */
	let newsArticles = [];
	let loading = true;
	/** @type {string|null} */
	let error = null;
	/** @type {string|null} */
	let successMessage = null;

	// Fetch news articles from the API
	async function fetchNewsArticles() {
		loading = true;
		error = null;

		log.info('Fetching news articles');
		const { data, error: apiError } = await api.get('/api/news?limit=100&all=true');

		if (apiError) {
			log.error('Failed to fetch news articles', apiError);
			error = apiError;
		} else {
			log.debug('News articles fetched successfully', { count: data?.length });
			newsArticles = data || [];
		}

		loading = false;
	}

	/**
	 * Delete a news article
	 * @param {number} id - The ID of the news article to delete
	 */
	async function deleteNewsArticle(id) {
		if (!confirm('Are you sure you want to delete this news article?')) {
			return;
		}

		log.info(`Deleting news article ${id}`);
		const { success, error: apiError } = await api.delete(`/api/news/${id}`);

		if (success) {
			log.info(`News article ${id} deleted successfully`);
			// Remove the item from the list
			newsArticles = newsArticles.filter(item => item.id !== id);
			successMessage = 'News article deleted successfully';
		} else {
			log.error(`Failed to delete news article ${id}`, apiError);
			error = apiError || 'Failed to delete news article';
		}
	}

	/**
	 * Toggle the published status of a news article
	 * @param {{id: number, title: string, content: string, imageUrl?: string, published: boolean}} item - The news article to update
	 */
	async function togglePublished(item) {
		const newStatus = !item.published;
		log.info(`Toggling news article ${item.id} published status to ${newStatus}`);

		const { success, data, error: apiError } = await api.put(`/api/news/${item.id}`, {
			...item,
			published: newStatus
		});

		if (success && data) {
			log.info(`News article ${item.id} updated successfully`);
			// Update the item in the list
			newsArticles = newsArticles.map(i =>
				i.id === item.id ? data : i
			);
			successMessage = `News article ${newStatus ? 'published' : 'unpublished'} successfully`;
		} else {
			log.error(`Failed to update news article ${item.id}`, apiError);
			error = apiError || 'Failed to update news article';
		}
	}

	/**
	 * Extract plain text from HTML content for preview
	 * @param {string} html - HTML content
	 * @returns {string} Plain text
	 */
	function extractTextFromHtml(html) {
		if (!html) return '';
		// Simple HTML tag removal - in production you might want a more robust solution
		return html.replace(/<[^>]*>/g, '').substring(0, 150) + '...';
	}

	// Load news articles on mount
	onMount(() => {
		fetchNewsArticles();
	});
</script>

<svelte:head>
	<title>News Management - Admin</title>
</svelte:head>

<div class="news-admin-container">
	<div class="header">
		<h1>News Management</h1>
		<div class="header-actions">
			<a href="/admin/news/create" class="btn primary">Create New Article</a>
		</div>
	</div>

	{#if error}
		<div class="error-message">
			<p>{error}</p>
			<button class="btn secondary" on:click={() => error = null}>Dismiss</button>
		</div>
	{/if}

	{#if successMessage}
		<div class="success-message">
			<p>{successMessage}</p>
			<button class="btn secondary" on:click={() => successMessage = null}>Dismiss</button>
		</div>
	{/if}

	{#if loading}
		<div class="loading">
			<p>Loading news articles...</p>
		</div>
	{:else if newsArticles.length === 0}
		<div class="empty-state">
			<p>No news articles found. Create your first article to get started!</p>
			<a href="/admin/news/create" class="btn primary">Create Article</a>
		</div>
	{:else}
		<div class="news-grid">
			{#each newsArticles as article}
				<div class="news-item" class:unpublished={!article.published}>
					{#if article.imageUrl}
						<div class="news-image">
							<img src={article.imageUrl} alt={article.title} />
						</div>
					{/if}
					<div class="news-info">
						<h3>{article.title}</h3>
						<p class="content-preview">{extractTextFromHtml(article.content)}</p>
						<p class="date">Created: {new Date(article.createdAt).toLocaleDateString()}</p>
						<div class="status">
							Status:
							<span class={article.published ? 'published' : 'draft'}>
								{article.published ? 'Published' : 'Draft'}
							</span>
						</div>
					</div>
					<div class="news-actions">
						<button
							class="btn icon-btn"
							title={article.published ? 'Unpublish' : 'Publish'}
							on:click={() => togglePublished(article)}
						>
							{article.published ? '👁️' : '👁️‍🗨️'}
						</button>
						<a
							href={`/admin/news/edit/${article.id}`}
							class="btn icon-btn"
							title="Edit"
						>
							✏️
						</a>
						<button
							class="btn icon-btn delete"
							title="Delete"
							on:click={() => deleteNewsArticle(article.id)}
						>
							🗑️
						</button>
					</div>
				</div>
			{/each}
		</div>
	{/if}
</div>

<style>
	.news-admin-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 2rem;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 2rem;
	}

	.header-actions {
		display: flex;
		gap: 1rem;
		align-items: center;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		text-decoration: none;
		display: inline-flex;
		align-items: center;
		justify-content: center;
	}

	.primary {
		background-color: #4caf50;
		color: white;
	}

	.secondary {
		background-color: #f0f0f0;
		color: #333;
	}

	.icon-btn {
		padding: 0.5rem;
		font-size: 1.2rem;
		background: none;
	}

	.delete {
		color: #e53935;
	}

	.error-message {
		background-color: #ffebee;
		color: #c62828;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.success-message {
		background-color: #e8f5e8;
		color: #2e7d32;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.loading, .empty-state {
		text-align: center;
		padding: 3rem;
		background-color: #f5f5f5;
		border-radius: 4px;
	}

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1rem;
	}

	.news-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
		gap: 1.5rem;
	}

	.news-item {
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		background-color: white;
		position: relative;
	}

	.unpublished {
		background-color: #f5f5f5;
		border: 1px dashed #ccc;
	}

	.news-image {
		height: 200px;
		overflow: hidden;
	}

	.news-image img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.news-info {
		padding: 1rem;
	}

	.news-info h3 {
		margin: 0 0 0.5rem 0;
	}

	.content-preview {
		margin: 0.5rem 0;
		color: #666;
		display: -webkit-box;
		-webkit-line-clamp: 3;
		line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.date {
		font-size: 0.9rem;
		color: #888;
		margin: 0.5rem 0;
	}

	.status {
		font-size: 0.9rem;
		margin-top: 0.5rem;
	}

	.published {
		color: #2e7d32;
		font-weight: bold;
	}

	.draft {
		color: #f57c00;
		font-weight: bold;
	}

	.news-actions {
		display: flex;
		justify-content: flex-end;
		padding: 0.5rem;
		background-color: #f9f9f9;
		border-top: 1px solid #eee;
	}
</style>
