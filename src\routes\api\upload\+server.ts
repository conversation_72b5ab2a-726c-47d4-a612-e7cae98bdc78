import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import sharp from 'sharp';

// Get the directory name for the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Define the upload directory paths
const UPLOAD_DIR = path.resolve('static/uploads');
const GALLERY_DIR = path.join(UPLOAD_DIR, 'gallery');
const THUMBNAIL_DIR = path.join(GALLERY_DIR, 'thumbnails');

// Ensure the upload directories exist
try {
  if (!fs.existsSync(UPLOAD_DIR)) {
    fs.mkdirSync(UPLOAD_DIR, { recursive: true });
  }
  if (!fs.existsSync(GALLERY_DIR)) {
    fs.mkdirSync(GALLERY_DIR, { recursive: true });
  }
  if (!fs.existsSync(THUMBNAIL_DIR)) {
    fs.mkdirSync(THUMBNAIL_DIR, { recursive: true });
  }
} catch (error) {
  console.error('Error creating upload directories:', error);
}

// Helper function to generate a unique filename
function generateUniqueFilename(originalName: string): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 10);
  const extension = path.extname(originalName);
  const safeName = path.basename(originalName, extension)
    .replace(/[^a-z0-9]/gi, '-')
    .toLowerCase();
  
  return `${safeName}-${timestamp}-${randomString}${extension}`;
}

// Helper function to create a thumbnail from an image
async function createThumbnail(
  sourcePath: string, 
  targetPath: string, 
  width = 300
): Promise<void> {
  try {
    await sharp(sourcePath)
      .resize(width)
      .toFile(targetPath);
  } catch (error) {
    console.error('Error creating thumbnail:', error);
    throw error;
  }
}

// POST /api/upload - Upload a file (admin only)
export const POST: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }
  
  try {
    // Check if the request is multipart/form-data
    const contentType = request.headers.get('content-type') || '';
    if (!contentType.includes('multipart/form-data')) {
      return json({
        success: false,
        error: 'Request must be multipart/form-data'
      }, { status: 400 });
    }
    
    // Parse the form data
    const formData = await request.formData();
    const file = formData.get('file');
    const category = formData.get('category') || 'gallery';
    
    // Validate the file
    if (!file || !(file instanceof File)) {
      return json({
        success: false,
        error: 'No file uploaded'
      }, { status: 400 });
    }
    
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return json({
        success: false,
        error: 'File type not allowed. Allowed types: JPEG, PNG, GIF, WebP'
      }, { status: 400 });
    }
    
    // Generate a unique filename
    const uniqueFilename = generateUniqueFilename(file.name);
    
    // Define file paths
    const uploadPath = path.join(GALLERY_DIR, uniqueFilename);
    const thumbnailPath = path.join(THUMBNAIL_DIR, uniqueFilename);
    
    // Convert the file to a Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Save the file
    fs.writeFileSync(uploadPath, buffer);
    
    // Create a thumbnail
    await createThumbnail(uploadPath, thumbnailPath);
    
    // Return the file paths (relative to the static directory)
    const imageUrl = `/uploads/gallery/${uniqueFilename}`;
    const thumbnailUrl = `/uploads/gallery/thumbnails/${uniqueFilename}`;
    
    return json({
      success: true,
      data: {
        filename: uniqueFilename,
        originalName: file.name,
        size: file.size,
        type: file.type,
        imageUrl,
        thumbnailUrl
      }
    }, { status: 201 });
  } catch (error) {
    console.error('Error uploading file:', error);
    return json({
      success: false,
      error: 'Failed to upload file'
    }, { status: 500 });
  }
};
