<script>
	import { onMount } from 'svelte';
	import { api } from '$lib/utils/api';
	import logger from '$lib/client/logger';

	// Create a logger with context
	const log = logger.withContext('gallery-admin');

	// State variables
	/** @type {Array<{id: number, title: string, description?: string, imageUrl: string, thumbnailUrl: string, authorId?: number, createdAt: string, updatedAt: string, published: boolean}>} */
	let galleryItems = [];
	let loading = true;
	let syncing = false;
	/** @type {string|null} */
	let error = null;
	/** @type {string|null} */
	let syncMessage = null;

	// Fetch gallery items from the API
	async function fetchGalleryItems() {
		loading = true;
		error = null;

		log.info('Fetching gallery items');
		const { data, error: apiError } = await api.get('/api/gallery?limit=100&all=true');

		if (apiError) {
			log.error('Failed to fetch gallery items', apiError);
			error = apiError;
		} else {
			log.debug('Gallery items fetched successfully', { count: data?.length });
			galleryItems = data || [];
		}

		loading = false;
	}

	/**
	 * Delete a gallery item
	 * @param {number} id - The ID of the gallery item to delete
	 */
	async function deleteGalleryItem(id) {
		if (!confirm('Are you sure you want to delete this gallery item?')) {
			return;
		}

		log.info(`Deleting gallery item ${id}`);
		const { success, error: apiError } = await api.delete(`/api/gallery/${id}`);

		if (success) {
			log.info(`Gallery item ${id} deleted successfully`);
			// Remove the item from the list
			galleryItems = galleryItems.filter(item => item.id !== id);
		} else {
			log.error(`Failed to delete gallery item ${id}`, apiError);
			alert(apiError || 'Failed to delete gallery item');
		}
	}

	/**
	 * Toggle the published status of a gallery item
	 * @param {{id: number, title: string, description?: string, imageUrl: string, thumbnailUrl: string, published: boolean}} item - The gallery item to update
	 */
	async function togglePublished(item) {
		const newStatus = !item.published;
		log.info(`Toggling gallery item ${item.id} published status to ${newStatus}`);

		const { success, data, error: apiError } = await api.put(`/api/gallery/${item.id}`, {
			...item,
			published: newStatus
		});

		if (success && data) {
			log.info(`Gallery item ${item.id} updated successfully`);
			// Update the item in the list
			galleryItems = galleryItems.map(i =>
				i.id === item.id ? data : i
			);
		} else {
			log.error(`Failed to update gallery item ${item.id}`, apiError);
			alert(apiError || 'Failed to update gallery item');
		}
	}

	/**
	 * Sync uploaded files with the database
	 */
	async function syncUploads() {
		syncing = true;
		syncMessage = null;
		error = null;

		log.info('Syncing uploaded files with database');
		const { success, data, error: apiError } = await api.post('/api/admin/sync-uploads');

		if (success && data) {
			log.info('Sync completed successfully', data);
			syncMessage = `Sync completed: ${data.created} items created, ${data.skipped} skipped, ${data.processed} total processed`;

			if (data.errors && data.errors.length > 0) {
				syncMessage += `. Errors: ${data.errors.join(', ')}`;
			}

			// Refresh the gallery items
			await fetchGalleryItems();
		} else {
			log.error('Failed to sync uploads', apiError);
			error = apiError || 'Failed to sync uploads';
		}

		syncing = false;
	}

	// Load gallery items on mount
	onMount(() => {
		fetchGalleryItems();
	});
</script>

<svelte:head>
	<title>Gallery Management - Admin</title>
</svelte:head>

<div class="gallery-admin-container">
	<div class="header">
		<h1>Gallery Management</h1>
		<div class="header-actions">
			<button class="btn secondary" on:click={syncUploads} disabled={syncing}>
				{syncing ? 'Syncing...' : 'Sync Uploads'}
			</button>
			<a href="/admin/media" class="btn secondary">Media Library</a>
			<a href="/admin/gallery/upload" class="btn primary">Upload New Images</a>
		</div>
	</div>

	{#if error}
		<div class="error-message">
			<p>{error}</p>
			<button class="btn secondary" on:click={fetchGalleryItems}>Try Again</button>
		</div>
	{/if}

	{#if syncMessage}
		<div class="success-message">
			<p>{syncMessage}</p>
			<button class="btn secondary" on:click={() => syncMessage = null}>Dismiss</button>
		</div>
	{/if}

	{#if loading}
		<div class="loading">
			<p>Loading gallery items...</p>
		</div>
	{:else if galleryItems.length === 0}
		<div class="empty-state">
			<p>No gallery items found. Upload some images to get started!</p>
			<a href="/admin/gallery/upload" class="btn primary">Upload Images</a>
		</div>
	{:else}
		<div class="gallery-grid">
			{#each galleryItems as item}
				<div class="gallery-item" class:unpublished={!item.published}>
					<div class="gallery-image">
						<img src={item.thumbnailUrl} alt={item.title} />
					</div>
					<div class="gallery-info">
						<h3>{item.title}</h3>
						{#if item.description}
							<p class="description">{item.description}</p>
						{/if}
						<p class="date">Created: {new Date(item.createdAt).toLocaleDateString()}</p>
						<div class="status">
							Status:
							<span class={item.published ? 'published' : 'draft'}>
								{item.published ? 'Published' : 'Draft'}
							</span>
						</div>
					</div>
					<div class="gallery-actions">
						<button
							class="btn icon-btn"
							title={item.published ? 'Unpublish' : 'Publish'}
							on:click={() => togglePublished(item)}
						>
							{item.published ? '👁️' : '👁️‍🗨️'}
						</button>
						<a
							href={`/admin/gallery/edit/${item.id}`}
							class="btn icon-btn"
							title="Edit"
						>
							✏️
						</a>
						<button
							class="btn icon-btn delete"
							title="Delete"
							on:click={() => deleteGalleryItem(item.id)}
						>
							🗑️
						</button>
					</div>
				</div>
			{/each}
		</div>
	{/if}
</div>

<style>
	.gallery-admin-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 2rem;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 2rem;
	}

	.header-actions {
		display: flex;
		gap: 1rem;
		align-items: center;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		text-decoration: none;
		display: inline-flex;
		align-items: center;
		justify-content: center;
	}

	.primary {
		background-color: #4caf50;
		color: white;
	}

	.secondary {
		background-color: #f0f0f0;
		color: #333;
	}

	.icon-btn {
		padding: 0.5rem;
		font-size: 1.2rem;
		background: none;
	}

	.delete {
		color: #e53935;
	}

	.error-message {
		background-color: #ffebee;
		color: #c62828;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.success-message {
		background-color: #e8f5e8;
		color: #2e7d32;
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.loading, .empty-state {
		text-align: center;
		padding: 3rem;
		background-color: #f5f5f5;
		border-radius: 4px;
	}

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1rem;
	}

	.gallery-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
		gap: 1.5rem;
	}

	.gallery-item {
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		background-color: white;
		position: relative;
	}

	.unpublished {
		background-color: #f5f5f5;
		border: 1px dashed #ccc;
	}

	.gallery-image {
		height: 200px;
		overflow: hidden;
	}

	.gallery-image img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.gallery-info {
		padding: 1rem;
	}

	.gallery-info h3 {
		margin: 0 0 0.5rem 0;
	}

	.description {
		margin: 0.5rem 0;
		color: #666;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.date {
		font-size: 0.9rem;
		color: #888;
		margin: 0.5rem 0;
	}

	.status {
		font-size: 0.9rem;
		margin-top: 0.5rem;
	}

	.published {
		color: #2e7d32;
		font-weight: bold;
	}

	.draft {
		color: #f57c00;
		font-weight: bold;
	}

	.gallery-actions {
		display: flex;
		justify-content: flex-end;
		padding: 0.5rem;
		background-color: #f9f9f9;
		border-top: 1px solid #eee;
	}
</style>
