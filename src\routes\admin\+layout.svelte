<script>
  import { dev } from '$app/environment';
  import DebugPanel from '$lib/components/admin/DebugPanel.svelte';
  import ErrorBoundary from '$lib/components/ErrorBoundary.svelte';

  // Only show debug panel in development mode
  const showDebugPanel = dev;
</script>

<ErrorBoundary>
  <div class="admin-layout">
    <header class="admin-header">
      <div class="logo">
        <a href="/admin">FWFC Admin</a>
      </div>
      <nav class="admin-nav">
        <a href="/admin/gallery">Gallery</a>
        <a href="/admin/media">Media</a>
        <a href="/admin/hero-images">Hero Images</a>
        <a href="/admin/news">News</a>
        <a href="/admin/users">Users</a>
        <a href="/admin/site-settings">Site Settings</a>
        <a href="/">View Site</a>
      </nav>
    </header>

    <main class="admin-content">
      <slot />
    </main>

    <footer class="admin-footer">
      <p>Finn <PERSON> Fan Club Admin Panel &copy; {new Date().getFullYear()}</p>
    </footer>
  </div>

  {#if showDebugPanel}
    <DebugPanel />
  {/if}
</ErrorBoundary>

<style>
  .admin-layout {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  .admin-header {
    background-color: var(--theme-bg-secondary);
    color: var(--theme-text-primary);
    border-bottom: 1px solid var(--theme-border);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .logo a {
    color: var(--theme-accent-primary);
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: bold;
  }

  .logo a:hover {
    color: var(--theme-accent-primary-hover);
  }

  .admin-nav {
    display: flex;
    gap: 1.5rem;
  }

  .admin-nav a {
    color: var(--theme-text-primary);
    text-decoration: none;
    padding: 0.5rem 0;
    position: relative;
    transition: color 0.2s ease;
  }

  .admin-nav a:hover {
    color: var(--theme-accent-primary);
  }

  .admin-nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--theme-accent-primary);
    transition: width 0.3s;
  }

  .admin-nav a:hover::after {
    width: 100%;
  }

  .admin-content {
    flex: 1;
    padding: 2rem;
    background-color: var(--theme-bg-primary);
    color: var(--theme-text-primary);
  }

  .admin-footer {
    background-color: var(--theme-bg-secondary);
    color: var(--theme-text-secondary);
    border-top: 1px solid var(--theme-border);
    padding: 1rem 2rem;
    text-align: center;
  }
</style>
