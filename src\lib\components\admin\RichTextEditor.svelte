<script>
	import { onMount, createEventDispatcher } from 'svelte';
	import { browser } from '$app/environment';

	const dispatch = createEventDispatcher();

	// Props
	export let value = '';
	export let placeholder = 'Enter content...';
	export let disabled = false;
	export let height = 400;

	// State
	let editor;
	let editorContainer;
	let isReady = false;

	// Initialize TinyMCE editor
	async function initEditor() {
		if (!browser || !editorContainer) return;

		try {
			// Dynamically import TinyMCE to avoid SSR issues
			const tinymce = await import('tinymce/tinymce');
			
			// Import themes and plugins
			await import('tinymce/themes/silver');
			await import('tinymce/plugins/lists');
			await import('tinymce/plugins/link');
			await import('tinymce/plugins/image');
			await import('tinymce/plugins/table');
			await import('tinymce/plugins/code');
			await import('tinymce/plugins/help');
			await import('tinymce/plugins/wordcount');

			// Initialize the editor
			await tinymce.default.init({
				target: editorContainer,
				height: height,
				menubar: false,
				plugins: [
					'lists', 'link', 'image', 'table', 'code', 'help', 'wordcount'
				],
				toolbar: 'undo redo | formatselect | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image | table | code | help',
				content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; }',
				setup: (ed) => {
					editor = ed;
					
					// Set initial content
					ed.on('init', () => {
						ed.setContent(value || '');
						isReady = true;
					});

					// Handle content changes
					ed.on('input change keyup', () => {
						const content = ed.getContent();
						value = content;
						dispatch('change', { content });
					});
				},
				disabled: disabled
			});

		} catch (error) {
			console.error('Failed to initialize TinyMCE:', error);
			// Fallback to textarea if TinyMCE fails to load
			createFallbackEditor();
		}
	}

	// Create fallback textarea editor
	function createFallbackEditor() {
		if (!editorContainer) return;
		
		const textarea = document.createElement('textarea');
		textarea.value = value || '';
		textarea.placeholder = placeholder;
		textarea.disabled = disabled;
		textarea.style.width = '100%';
		textarea.style.height = `${height}px`;
		textarea.style.padding = '0.75rem';
		textarea.style.border = '1px solid #ddd';
		textarea.style.borderRadius = '4px';
		textarea.style.fontFamily = 'inherit';
		textarea.style.fontSize = '1rem';
		textarea.style.resize = 'vertical';

		textarea.addEventListener('input', (e) => {
			value = e.target.value;
			dispatch('change', { content: value });
		});

		editorContainer.appendChild(textarea);
		isReady = true;
	}

	// Update editor content when value prop changes
	$: if (isReady && editor && value !== editor.getContent()) {
		editor.setContent(value || '');
	}

	// Update disabled state
	$: if (isReady && editor) {
		editor.mode.set(disabled ? 'readonly' : 'design');
	}

	onMount(() => {
		initEditor();

		// Cleanup on unmount
		return () => {
			if (editor) {
				editor.destroy();
			}
		};
	});
</script>

<div class="rich-text-editor">
	<div bind:this={editorContainer} class="editor-container"></div>
	
	{#if !isReady}
		<div class="loading">
			<p>Loading editor...</p>
		</div>
	{/if}
</div>

<style>
	.rich-text-editor {
		position: relative;
		width: 100%;
	}

	.editor-container {
		width: 100%;
	}

	.loading {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: rgba(255, 255, 255, 0.9);
		border: 1px solid #ddd;
		border-radius: 4px;
		min-height: 200px;
	}

	.loading p {
		margin: 0;
		color: #666;
	}

	/* Global styles for TinyMCE */
	:global(.tox-tinymce) {
		border: 1px solid #ddd !important;
		border-radius: 4px !important;
	}

	:global(.tox-toolbar) {
		border-bottom: 1px solid #ddd !important;
	}

	:global(.tox-edit-area) {
		border: none !important;
	}
</style>
