<script lang="ts">
	import { page } from '$app/stores';
	import { onMount } from 'svelte';
	import ThemeToggle from '$lib/components/ThemeToggle.svelte';

	// Placeholder for logo - we'll replace this with a real logo later
	let logoUrl = '/images/logo.png';

	// Message of the day
	let messageOfTheDay = '';
	let motdIndex = 0;
	let motdList = [
		'<PERSON> was born on December 23, 2002, in Vancouver, Canada.',
		'<PERSON> plays <PERSON> in the Netflix series Stranger Things.',
		'<PERSON> is the lead vocalist and guitarist for the rock band The Aubreys.',
		'<PERSON> made his directorial debut with the short film "Night Shifts" in 2020.',
		'<PERSON> starred as <PERSON> in the horror films "It" (2017) and "It Chapter Two" (2019).'
	];

	// Rotate message of the day
	function rotateMOTD() {
		motdIndex = (motdIndex + 1) % motdList.length;
		messageOfTheDay = motdList[motdIndex];
	}

	onMount(() => {
		// Set initial message
		messageOfTheDay = motdList[motdIndex];

		// Rotate message every 10 seconds
		const interval = setInterval(rotateMOTD, 10000);

		// Clean up interval on component destruction
		return () => clearInterval(interval);

		// In a real app, we would fetch these from the API
		// fetch('/api/motd').then(r => r.json()).then(data => {
		//   motdList = data;
		//   messageOfTheDay = motdList[0];
		// });
	});
</script>

<header>
	<div class="logo">
		<a href="/">
			<img src={logoUrl} alt="Finn Wolfhard Fan Club" />
			<span class="site-name">Finn Wolfhard Fan Club</span>
		</a>
	</div>

	<div class="message-of-the-day">
		<p>{messageOfTheDay}</p>
	</div>

	<nav>
		<ul>
			<li aria-current={$page.url.pathname === '/' ? 'page' : undefined}>
				<a href="/">Home</a>
			</li>
			<li aria-current={$page.url.pathname === '/gallery' ? 'page' : undefined}>
				<a href="/gallery">Gallery</a>
			</li>
			<li aria-current={$page.url.pathname === '/news' ? 'page' : undefined}>
				<a href="/news">News</a>
			</li>
			<li aria-current={$page.url.pathname === '/message-board' ? 'page' : undefined}>
				<a href="/message-board">Message Board</a>
			</li>
			<li aria-current={$page.url.pathname === '/about' ? 'page' : undefined}>
				<a href="/about">About</a>
			</li>
			<li class="theme-toggle-item">
				<ThemeToggle />
			</li>
			<li class="login-button">
				<a href="/login">Login</a>
			</li>
		</ul>
	</nav>
</header>

<style>
	header {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 1rem;
		background-color: var(--theme-nav-bg);
		border-bottom: 1px solid var(--theme-border);
		color: var(--theme-nav-text);
	}

	.logo {
		display: flex;
		align-items: center;
		margin-bottom: 0.5rem;
	}

	.logo a {
		display: flex;
		align-items: center;
		text-decoration: none;
		color: var(--theme-nav-text);
	}

	.logo img {
		width: 50px;
		height: 50px;
		margin-right: 10px;
		border-radius: 50%;
		object-fit: cover;
	}

	.site-name {
		font-size: 1.5rem;
		font-weight: bold;
	}

	.message-of-the-day {
		width: 100%;
		text-align: center;
		padding: 0.5rem;
		background-color: var(--theme-bg-secondary);
		border-radius: 4px;
		margin: 0.5rem 0;
		font-style: italic;
		color: var(--theme-text-secondary);
	}

	.message-of-the-day p {
		margin: 0;
	}

	nav {
		width: 100%;
	}

	ul {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		align-items: center;
		padding: 0;
		margin: 0;
		list-style: none;
		gap: 0.5rem;
	}

	li {
		position: relative;
	}

	li[aria-current='page']::after {
		content: '';
		position: absolute;
		bottom: -2px;
		left: 0;
		width: 100%;
		height: 2px;
		background-color: var(--theme-accent-primary);
	}

	nav a {
		display: inline-block;
		padding: 0.5rem 0.2rem;
		color: var(--theme-nav-text);
		font-weight: 600;
		text-decoration: none;
		transition: color 0.2s;
	}

	nav a:hover {
		color: var(--theme-accent-primary);
	}

	.theme-toggle-item {
		display: flex;
		align-items: center;
	}

	.login-button a {
		background-color: var(--theme-accent-primary);
		color: white;
		padding: 0.5rem 1rem;
		border-radius: 4px;
	}

	.login-button a:hover {
		background-color: var(--theme-accent-primary-hover);
	}

	@media (min-width: 768px) {
		header {
			flex-direction: row;
			justify-content: space-between;
		}

		.logo {
			margin-bottom: 0;
		}

		.message-of-the-day {
			width: auto;
			flex: 1;
			margin: 0 1rem;
		}

		nav {
			width: auto;
		}
	}
</style>
