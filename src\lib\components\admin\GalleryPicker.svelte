<script>
	import { onMount, createEventDispatcher } from 'svelte';
	import { api } from '$lib/utils/api';

	const dispatch = createEventDispatcher();

	// Props
	export let isOpen = false;
	export let selectedImageUrl = '';

	// State
	let galleryItems = [];
	let loading = true;
	let error = null;
	let searchQuery = '';
	let selectedItem = null;

	// Reactive filtered gallery items
	$: filteredItems = galleryItems.filter(item => {
		if (!searchQuery) return true;
		return item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
			   (item.description && item.description.toLowerCase().includes(searchQuery.toLowerCase()));
	});

	// Load gallery items
	async function loadGalleryItems() {
		if (!isOpen) return;
		
		loading = true;
		error = null;

		try {
			const { data, error: apiError } = await api.get('/api/gallery?all=true&limit=100');
			
			if (apiError) {
				throw new Error(apiError);
			}
			
			galleryItems = data || [];
			
			// Pre-select current image if it matches
			if (selectedImageUrl) {
				selectedItem = galleryItems.find(item => 
					item.imageUrl === selectedImageUrl || 
					item.thumbnailUrl === selectedImageUrl
				);
			}
		} catch (err) {
			console.error('Error loading gallery items:', err);
			error = err.message || 'Failed to load gallery items';
		} finally {
			loading = false;
		}
	}

	// Handle item selection
	function selectItem(item) {
		selectedItem = item;
	}

	// Confirm selection
	function confirmSelection() {
		if (selectedItem) {
			dispatch('select', {
				imageUrl: selectedItem.imageUrl,
				thumbnailUrl: selectedItem.thumbnailUrl,
				title: selectedItem.title,
				galleryId: selectedItem.id
			});
		}
		closeModal();
	}

	// Close modal
	function closeModal() {
		isOpen = false;
		selectedItem = null;
		searchQuery = '';
		dispatch('close');
	}

	// Handle escape key
	function handleKeydown(event) {
		if (event.key === 'Escape') {
			closeModal();
		}
	}

	// Load items when modal opens
	$: if (isOpen) {
		loadGalleryItems();
	}
</script>

<svelte:window on:keydown={handleKeydown} />

{#if isOpen}
	<div class="modal-overlay" on:click={closeModal}>
		<div class="modal-content" on:click|stopPropagation>
			<div class="modal-header">
				<h2>Select Image from Gallery</h2>
				<button class="close-btn" on:click={closeModal}>&times;</button>
			</div>

			<div class="modal-body">
				<!-- Search -->
				<div class="search-section">
					<input
						type="text"
						placeholder="Search gallery images..."
						bind:value={searchQuery}
						class="search-input"
					/>
				</div>

				<!-- Loading State -->
				{#if loading}
					<div class="loading">
						<p>Loading gallery images...</p>
					</div>
				{:else if error}
					<div class="error">
						<p>Error: {error}</p>
						<button class="btn secondary" on:click={loadGalleryItems}>Retry</button>
					</div>
				{:else if filteredItems.length === 0}
					<div class="empty-state">
						<p>No gallery images found.</p>
						{#if searchQuery}
							<p>Try adjusting your search terms.</p>
						{:else}
							<p>Upload some images to the gallery first.</p>
						{/if}
					</div>
				{:else}
					<!-- Gallery Grid -->
					<div class="gallery-grid">
						{#each filteredItems as item}
							<div 
								class="gallery-item"
								class:selected={selectedItem?.id === item.id}
								on:click={() => selectItem(item)}
							>
								<div class="image-container">
									<img 
										src={item.thumbnailUrl || item.imageUrl} 
										alt={item.title}
										loading="lazy"
									/>
									{#if selectedItem?.id === item.id}
										<div class="selected-overlay">
											<span class="checkmark">✓</span>
										</div>
									{/if}
								</div>
								<div class="item-info">
									<h4>{item.title}</h4>
									{#if item.description}
										<p class="description">{item.description.substring(0, 60)}...</p>
									{/if}
									<p class="date">{new Date(item.createdAt).toLocaleDateString()}</p>
								</div>
							</div>
						{/each}
					</div>
				{/if}
			</div>

			<div class="modal-footer">
				<button class="btn secondary" on:click={closeModal}>Cancel</button>
				<button 
					class="btn primary" 
					on:click={confirmSelection}
					disabled={!selectedItem}
				>
					Select Image
				</button>
			</div>
		</div>
	</div>
{/if}

<style>
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
		padding: 1rem;
	}

	.modal-content {
		background: white;
		border-radius: 8px;
		width: 100%;
		max-width: 900px;
		max-height: 90vh;
		display: flex;
		flex-direction: column;
		overflow: hidden;
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1.5rem;
		border-bottom: 1px solid #eee;
	}

	.modal-header h2 {
		margin: 0;
	}

	.close-btn {
		background: none;
		border: none;
		font-size: 2rem;
		cursor: pointer;
		color: #666;
		padding: 0;
		width: 2rem;
		height: 2rem;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.close-btn:hover {
		color: #333;
	}

	.modal-body {
		flex: 1;
		overflow-y: auto;
		padding: 1.5rem;
	}

	.search-section {
		margin-bottom: 1.5rem;
	}

	.search-input {
		width: 100%;
		padding: 0.75rem;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 1rem;
	}

	.loading, .error, .empty-state {
		text-align: center;
		padding: 3rem;
		color: #666;
	}

	.error {
		color: #c62828;
	}

	.gallery-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
		gap: 1rem;
	}

	.gallery-item {
		border: 2px solid transparent;
		border-radius: 8px;
		overflow: hidden;
		cursor: pointer;
		transition: all 0.2s ease;
		background: white;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}

	.gallery-item:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
	}

	.gallery-item.selected {
		border-color: #4caf50;
		box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
	}

	.image-container {
		position: relative;
		height: 150px;
		overflow: hidden;
	}

	.image-container img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.selected-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(76, 175, 80, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.checkmark {
		color: white;
		font-size: 2rem;
		font-weight: bold;
	}

	.item-info {
		padding: 1rem;
	}

	.item-info h4 {
		margin: 0 0 0.5rem 0;
		font-size: 0.9rem;
		line-height: 1.2;
	}

	.description {
		margin: 0.25rem 0;
		font-size: 0.8rem;
		color: #666;
		line-height: 1.3;
	}

	.date {
		margin: 0.25rem 0 0 0;
		font-size: 0.75rem;
		color: #888;
	}

	.modal-footer {
		display: flex;
		justify-content: flex-end;
		gap: 1rem;
		padding: 1.5rem;
		border-top: 1px solid #eee;
		background-color: #f9f9f9;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		transition: opacity 0.2s ease;
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.btn.primary {
		background-color: #4caf50;
		color: white;
	}

	.btn.secondary {
		background-color: #f0f0f0;
		color: #333;
	}

	.btn:hover:not(:disabled) {
		opacity: 0.9;
	}
</style>
