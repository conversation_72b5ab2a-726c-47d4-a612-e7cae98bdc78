<script lang="ts">
	export let title = 'Error';
	export let message: string;
	export let type: 'error' | 'warning' | 'info' = 'error';
	export let dismissible = false;
	export let onDismiss: (() => void) | undefined = undefined;

	function handleDismiss() {
		if (onDismiss) {
			onDismiss();
		}
	}

	function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Escape' && dismissible) {
			handleDismiss();
		}
	}
</script>

<div 
	class="error-message" 
	class:error={type === 'error'}
	class:warning={type === 'warning'}
	class:info={type === 'info'}
	role="alert"
	aria-live="assertive"
	on:keydown={handleKeyDown}
	tabindex={dismissible ? 0 : -1}
>
	<div class="error-content">
		<div class="error-icon" aria-hidden="true">
			{#if type === 'error'}
				⚠️
			{:else if type === 'warning'}
				⚠️
			{:else}
				ℹ️
			{/if}
		</div>
		<div class="error-text">
			<h3 class="error-title">{title}</h3>
			<p class="error-description">{message}</p>
		</div>
		{#if dismissible}
			<button 
				class="dismiss-button"
				onclick={handleDismiss}
				aria-label="Dismiss {type} message"
				type="button"
			>
				×
			</button>
		{/if}
	</div>
</div>

<style>
	.error-message {
		border-radius: 8px;
		padding: 1rem;
		margin: 1rem 0;
		border: 1px solid;
		display: flex;
		align-items: flex-start;
	}

	.error-message.error {
		background-color: var(--theme-accent-danger);
		border-color: var(--theme-accent-danger);
		color: white;
	}

	.error-message.warning {
		background-color: var(--theme-accent-warning);
		border-color: var(--theme-accent-warning);
		color: white;
	}

	.error-message.info {
		background-color: var(--theme-accent-info);
		border-color: var(--theme-accent-info);
		color: white;
	}

	.error-content {
		display: flex;
		align-items: flex-start;
		gap: 0.75rem;
		width: 100%;
	}

	.error-icon {
		font-size: 1.25rem;
		flex-shrink: 0;
		margin-top: 0.125rem;
	}

	.error-text {
		flex: 1;
	}

	.error-title {
		margin: 0 0 0.5rem 0;
		font-size: 1.125rem;
		font-weight: 600;
	}

	.error-description {
		margin: 0;
		line-height: 1.5;
	}

	.dismiss-button {
		background: none;
		border: none;
		color: inherit;
		font-size: 1.5rem;
		cursor: pointer;
		padding: 0;
		width: 24px;
		height: 24px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 4px;
		flex-shrink: 0;
		transition: background-color 0.2s ease;
	}

	.dismiss-button:hover,
	.dismiss-button:focus {
		background-color: rgba(255, 255, 255, 0.2);
		outline: 2px solid rgba(255, 255, 255, 0.5);
		outline-offset: 1px;
	}

	.error-message:focus {
		outline: 2px solid rgba(255, 255, 255, 0.8);
		outline-offset: 2px;
	}
</style>
