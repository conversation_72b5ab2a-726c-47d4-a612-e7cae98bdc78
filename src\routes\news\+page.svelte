<script>
	// News data
	const newsArticles = [
		{
			id: 1,
			title: '<PERSON> Announces New Music Project',
			excerpt: 'The Stranger Things star is working on a new album with his band The Aubreys.',
			content: `
				<p><PERSON>, known for his role as <PERSON> in the hit Netflix series Stranger Things, has announced that he is working on a new album with his band, The Aubreys.</p>
				
				<p>The band, which <PERSON><PERSON> formed after the dissolution of his previous musical project Calpurnia, has been gaining traction in the indie music scene. Their unique sound, which blends elements of indie rock, post-punk, and alternative, has resonated with fans of <PERSON><PERSON>'s acting work as well as music enthusiasts.</p>
				
				<p>"We've been working on this album for a while now, and I'm really excited for people to hear it," <PERSON><PERSON> said in a recent interview. "It's a bit different from what we've done before, but I think fans will really connect with it."</p>
				
				<p>The album, which is yet to be titled, is expected to be released later this year. The band has already released a few singles, which have been well-received by critics and fans alike.</p>
				
				<p><PERSON><PERSON>'s ability to balance his successful acting career with his passion for music continues to impress fans and industry professionals. Despite his busy schedule filming for various projects, including the upcoming season of Stranger Things, <PERSON><PERSON> has managed to dedicate time to his musical endeavors.</p>
				
				<p><PERSON> Aubreys' new album is highly anticipated, and fans are eager to see what direction <PERSON><PERSON> and his bandmates will take with their music.</p>
			`,
			imageUrl: '/images/news-1.jpg',
			date: '2025-05-10',
			author: '<PERSON> <PERSON>',
			category: 'Music'
		},
		{
			id: 2,
			title: 'Exclusive Interview with <PERSON> Wolf<PERSON>',
			excerpt: 'We sat down with <PERSON> to discuss his upcoming projects and career highlights.',
			content: `
				<p>In an exclusive interview with our fan club, <PERSON> <PERSON><PERSON> opened up about his upcoming projects, career highlights, and future aspirations.</p>
				
				<p>Wolfhard, who rose to fame with his role in Stranger Things, has since expanded his portfolio with roles in films like IT, The Goldfinch, and Ghostbusters: Afterlife. He has also ventured into directing and music, showcasing his versatility as an artist.</p>
				
				<p>"I've been really fortunate to work on projects that I'm passionate about," Wolfhard told us. "Each role has taught me something different, and I'm grateful for all the opportunities I've had."</p>
				
				<p>When asked about his favorite role to date, Wolfhard mentioned that Mike Wheeler from Stranger Things holds a special place in his heart. "It was my first major role, and it changed my life in so many ways. The cast and crew have become like family to me."</p>
				
				<p>Wolfhard also discussed his directorial aspirations, revealing that he's working on a short film that he wrote and will direct. "Directing is something I've always been interested in. I love storytelling in all its forms, and directing allows me to tell stories from a different perspective."</p>
				
				<p>As for future projects, Wolfhard hinted at some exciting collaborations but remained tight-lipped about the details. "There are a few things in the works that I can't talk about yet, but I think fans will be excited when they're announced."</p>
				
				<p>Wolfhard also took the time to express his gratitude to his fans. "The support from fans means everything to me. It's what allows me to keep doing what I love, and I never take that for granted."</p>
				
				<p>Stay tuned for more updates on Finn Wolfhard's upcoming projects!</p>
			`,
			imageUrl: '/images/news-2.jpg',
			date: '2025-05-05',
			author: 'Michael Chen',
			category: 'Interviews'
		},
		{
			id: 3,
			title: 'Finn Wolfhard\'s Directorial Debut Gets Rave Reviews',
			excerpt: 'Critics are praising Finn\'s first feature film as director.',
			content: `
				<p>Finn Wolfhard's directorial debut, a coming-of-age drama titled "Echoes of Youth," has received rave reviews from critics following its premiere at the Toronto International Film Festival.</p>
				
				<p>The film, which Wolfhard both wrote and directed, tells the story of a teenage musician navigating the complexities of fame, friendship, and identity. Drawing from his own experiences in the entertainment industry, Wolfhard has crafted a poignant and authentic narrative that resonates with audiences.</p>
				
				<p>"Echoes of Youth" stars several up-and-coming actors, with Wolfhard making a brief cameo appearance. The film's soundtrack, which features original music composed by Wolfhard and his band The Aubreys, has also been praised for its contribution to the storytelling.</p>
				
				<p>Critics have commended Wolfhard's directorial style, noting his confident visual storytelling and ability to elicit strong performances from his cast. The Hollywood Reporter called the film "a remarkably assured debut from a multitalented artist," while Variety described it as "a sensitive and insightful exploration of adolescence in the spotlight."</p>
				
				<p>At just 22 years old, Wolfhard has already established himself as a versatile talent in the entertainment industry. His successful transition from actor to director has drawn comparisons to other actor-directors like Greta Gerwig and Jordan Peele.</p>
				
				<p>"I've been fortunate to work with some incredible directors throughout my career, and I've learned something from each of them," Wolfhard said during a post-screening Q&A. "Directing has always been a dream of mine, and I'm overwhelmed by the positive response to the film."</p>
				
				<p>"Echoes of Youth" is set for a wider release later this year, and there's already buzz about potential award nominations for Wolfhard's directorial work.</p>
			`,
			imageUrl: '/images/news-3.jpg',
			date: '2025-04-28',
			author: 'Emily Rodriguez',
			category: 'Movies'
		},
		{
			id: 4,
			title: 'Stranger Things Final Season: What We Know So Far',
			excerpt: 'Details about the highly anticipated final season of the Netflix hit series.',
			content: `
				<p>As the beloved Netflix series Stranger Things approaches its final season, fans are eagerly anticipating what's in store for the residents of Hawkins, Indiana. While the creators, the Duffer Brothers, have kept many details under wraps, some information has been revealed about the upcoming season.</p>
				
				<p>Finn Wolfhard, who plays Mike Wheeler, has described the final season as "epic" and "emotional" in recent interviews. "It's definitely the biggest season yet in terms of scale," Wolfhard said. "The stakes are higher than ever, and I think fans will be satisfied with how everything comes together."</p>
				
				<p>The final season is expected to answer many of the lingering questions from previous seasons and provide closure for the beloved characters. The Duffer Brothers have confirmed that the season will consist of eight episodes, with the finale being feature-length.</p>
				
				<p>Production for the final season began earlier this year, with the cast and crew returning to the set. Behind-the-scenes photos shared by the cast have given fans glimpses of what to expect, including reunions between favorite characters.</p>
				
				<p>While plot details remain scarce, it has been confirmed that all the main cast members, including Wolfhard, Millie Bobby Brown, Noah Schnapp, and Gaten Matarazzo, will return for the final season. There are also rumors of new characters being introduced, though these have not been officially confirmed.</p>
				
				<p>The release date for the final season has not yet been announced, but it is expected to premiere on Netflix sometime in 2026. As we get closer to the release, more details are likely to be revealed.</p>
				
				<p>Stay tuned for more updates on the final season of Stranger Things!</p>
			`,
			imageUrl: '/images/news-4.jpg',
			date: '2025-04-15',
			author: 'David Thompson',
			category: 'TV'
		}
	];
	
	// Filter state
	let selectedCategory = 'all';
	let searchQuery = '';
	
	// Computed filtered news articles
	$: filteredNews = newsArticles.filter(article => {
		// Filter by category
		const categoryMatch = selectedCategory === 'all' || article.category.toLowerCase() === selectedCategory.toLowerCase();
		
		// Filter by search query
		const searchMatch = searchQuery === '' || 
			article.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
			article.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
			article.content.toLowerCase().includes(searchQuery.toLowerCase());
		
		return categoryMatch && searchMatch;
	});
	
	// Categories for filter
	const categories = [
		{ id: 'all', name: 'All Categories' },
		{ id: 'tv', name: 'TV Shows' },
		{ id: 'movies', name: 'Movies' },
		{ id: 'music', name: 'Music' },
		{ id: 'interviews', name: 'Interviews' }
	];
	
	// Handle category change
	function handleCategoryChange(event) {
		selectedCategory = event.target.value;
	}
	
	// Handle search input
	function handleSearchInput(event) {
		searchQuery = event.target.value;
	}
</script>

<svelte:head>
	<title>News - Finn Wolfhard Fan Club</title>
	<meta name="description" content="Latest news and updates about actor and musician Finn Wolfhard" />
</svelte:head>

<div class="news-container">
	<h1>Latest News</h1>
	
	<div class="news-filters">
		<div class="search-box">
			<input 
				type="text" 
				placeholder="Search news..." 
				value={searchQuery}
				on:input={handleSearchInput}
			/>
		</div>
		
		<div class="category-filter">
			<select value={selectedCategory} on:change={handleCategoryChange}>
				{#each categories as category}
					<option value={category.id}>{category.name}</option>
				{/each}
			</select>
		</div>
	</div>
	
	{#if filteredNews.length === 0}
		<div class="no-results">
			<p>No news articles match your search criteria. Please try a different search or category.</p>
		</div>
	{:else}
		<div class="news-grid">
			{#each filteredNews as article}
				<div class="news-card">
					<a href={`/news/${article.id}`}>
						<div class="news-image">
							<img src={article.imageUrl} alt={article.title} />
						</div>
						<div class="news-content">
							<div class="news-meta">
								<span class="category">{article.category}</span>
								<span class="date">{article.date}</span>
							</div>
							<h2>{article.title}</h2>
							<p class="excerpt">{article.excerpt}</p>
							<span class="read-more">Read More</span>
						</div>
					</a>
				</div>
			{/each}
		</div>
	{/if}
</div>

<style>
	.news-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 1rem;
	}
	
	h1 {
		text-align: center;
		margin-bottom: 2rem;
	}
	
	.news-filters {
		display: flex;
		flex-direction: column;
		gap: 1rem;
		margin-bottom: 2rem;
	}
	
	.search-box input {
		width: 100%;
		padding: 0.8rem;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 1rem;
	}
	
	.category-filter select {
		width: 100%;
		padding: 0.8rem;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 1rem;
		background-color: white;
	}
	
	.news-grid {
		display: grid;
		grid-template-columns: 1fr;
		gap: 2rem;
	}
	
	.news-card {
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
		transition: transform 0.3s ease;
	}
	
	.news-card:hover {
		transform: translateY(-5px);
	}
	
	.news-card a {
		text-decoration: none;
		color: inherit;
		display: flex;
		flex-direction: column;
	}
	
	.news-image {
		height: 200px;
		overflow: hidden;
	}
	
	.news-image img {
		width: 100%;
		height: 100%;
		object-fit: cover;
		transition: transform 0.3s ease;
	}
	
	.news-card:hover .news-image img {
		transform: scale(1.05);
	}
	
	.news-content {
		padding: 1.5rem;
	}
	
	.news-meta {
		display: flex;
		justify-content: space-between;
		margin-bottom: 0.5rem;
		font-size: 0.9rem;
	}
	
	.category {
		color: var(--color-theme-1);
		font-weight: bold;
	}
	
	.date {
		color: #666;
	}
	
	.news-content h2 {
		margin-top: 0;
		margin-bottom: 1rem;
		font-size: 1.5rem;
		color: #333;
	}
	
	.excerpt {
		margin-bottom: 1rem;
		line-height: 1.5;
	}
	
	.read-more {
		display: inline-block;
		color: var(--color-theme-1);
		font-weight: bold;
	}
	
	.no-results {
		text-align: center;
		padding: 2rem;
		background-color: #f8f8f8;
		border-radius: 8px;
	}
	
	@media (min-width: 768px) {
		.news-filters {
			flex-direction: row;
			justify-content: space-between;
		}
		
		.search-box {
			flex: 1;
			margin-right: 1rem;
		}
		
		.category-filter {
			width: 250px;
		}
		
		.news-card a {
			flex-direction: row;
		}
		
		.news-image {
			width: 300px;
			height: auto;
		}
		
		.news-content {
			flex: 1;
		}
	}
	
	@media (min-width: 1024px) {
		.news-grid {
			grid-template-columns: repeat(2, 1fr);
		}
		
		.news-card a {
			flex-direction: column;
		}
		
		.news-image {
			width: 100%;
			height: 200px;
		}
	}
</style>
