<script>
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';

	// Props from page data
	export let data;

	// Extract data
	$: articles = data.articles;
	$: pagination = data.pagination;

	// Filter state
	let searchQuery = '';

	/**
	 * Format date for display
	 * @param {string} dateString - ISO date string
	 * @returns {string} Formatted date
	 */
	function formatDate(dateString) {
		const date = new Date(dateString);
		return date.toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'long',
			day: 'numeric'
		});
	}

	/**
	 * Extract plain text from HTML content for preview
	 * @param {string} html - HTML content
	 * @returns {string} Plain text
	 */
	function extractTextFromHtml(html) {
		if (!html) return '';
		// Simple HTML tag removal
		return html.replace(/<[^>]*>/g, '').substring(0, 200) + '...';
	}

	// Computed filtered news articles (client-side search)
	$: filteredNews = articles.filter(article => {
		if (searchQuery === '') return true;
		
		const searchLower = searchQuery.toLowerCase();
		return article.title.toLowerCase().includes(searchLower) || 
			   article.content.toLowerCase().includes(searchLower);
	});

	/**
	 * Navigate to a specific page
	 * @param {number} pageNum - Page number to navigate to
	 */
	function goToPage(pageNum) {
		const url = new URL($page.url);
		url.searchParams.set('page', pageNum.toString());
		goto(url.toString());
	}

	/**
	 * Handle search input
	 */
	function handleSearchInput(event) {
		searchQuery = event.target.value;
	}
</script>

<svelte:head>
	<title>News - FWFC</title>
	<meta name="description" content="Latest news and updates from FWFC. Stay informed about our activities, events, and announcements." />
</svelte:head>

<div class="news-page">
	<!-- Page Header -->
	<header class="page-header">
		<div class="container">
			<h1>Latest News</h1>
			<p>Stay up to date with our latest news and announcements</p>
			<nav class="breadcrumb">
				<a href="/">Home</a> / <span>News</span>
			</nav>
		</div>
	</header>

	<!-- News Content -->
	<main class="news-content">
		<div class="container">
			<!-- Search -->
			<div class="news-filters">
				<div class="search-box">
					<input 
						type="text" 
						placeholder="Search news..." 
						value={searchQuery}
						on:input={handleSearchInput}
					/>
				</div>
			</div>

			{#if filteredNews.length === 0}
				<div class="empty-state">
					{#if searchQuery}
						<h2>No Results Found</h2>
						<p>No news articles match your search for "{searchQuery}".</p>
						<p>Try a different search term or browse all articles.</p>
					{:else}
						<h2>No News Articles</h2>
						<p>There are no published news articles at the moment.</p>
						<p>Check back soon for updates!</p>
					{/if}
					<a href="/" class="btn primary">Back to Home</a>
				</div>
			{:else}
				<div class="articles-grid">
					{#each filteredNews as article}
						<article class="article-card">
							{#if article.imageUrl}
								<div class="article-image">
									<img src={article.imageUrl} alt={article.title} />
								</div>
							{/if}
							<div class="article-content">
								<header class="article-header">
									<h2>
										<a href="/news/{article.id}">{article.title}</a>
									</h2>
									<time class="article-date" datetime={article.createdAt}>
										{formatDate(article.createdAt)}
									</time>
								</header>
								<div class="article-excerpt">
									<p>{extractTextFromHtml(article.content)}</p>
								</div>
								<footer class="article-footer">
									<a href="/news/{article.id}" class="read-more">
										Read Full Article →
									</a>
								</footer>
							</div>
						</article>
					{/each}
				</div>
			{/if}
		</div>
	</main>
</div>

<style>
	.news-page {
		min-height: 100vh;
	}

	.container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.page-header {
		background: linear-gradient(135deg, var(--theme-accent-primary) 0%, var(--theme-accent-primary-hover) 100%);
		color: white;
		padding: 4rem 0 2rem;
		text-align: center;
	}

	.page-header h1 {
		font-size: 3rem;
		margin: 0 0 1rem 0;
		font-weight: bold;
	}

	.page-header p {
		font-size: 1.2rem;
		margin: 0 0 1rem 0;
		opacity: 0.9;
	}

	.breadcrumb {
		font-size: 1rem;
		opacity: 0.8;
	}

	.breadcrumb a {
		color: white;
		text-decoration: none;
	}

	.breadcrumb a:hover {
		text-decoration: underline;
	}

	.news-content {
		padding: 3rem 0;
	}

	.news-filters {
		margin-bottom: 2rem;
	}

	.search-box input {
		width: 100%;
		max-width: 400px;
		padding: 0.8rem;
		border: 1px solid var(--theme-border);
		border-radius: 4px;
		font-size: 1rem;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
	}

	.search-box input:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
	}

	.empty-state {
		text-align: center;
		padding: 4rem 2rem;
		background-color: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		border-radius: 8px;
	}

	.empty-state h2 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
	}

	.empty-state p {
		margin: 0.5rem 0;
		color: var(--theme-text-secondary);
	}

	.articles-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
		gap: 2rem;
		margin-bottom: 3rem;
	}

	.article-card {
		background: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 4px 6px var(--theme-shadow);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.article-card:hover {
		transform: translateY(-5px);
		box-shadow: 0 8px 15px var(--theme-shadow-hover);
	}

	.article-image {
		height: 250px;
		overflow: hidden;
	}

	.article-image img {
		width: 100%;
		height: 100%;
		object-fit: cover;
		transition: transform 0.3s ease;
	}

	.article-card:hover .article-image img {
		transform: scale(1.05);
	}

	.article-content {
		padding: 1.5rem;
	}

	.article-header h2 {
		margin: 0 0 0.5rem 0;
		font-size: 1.5rem;
		line-height: 1.3;
	}

	.article-header h2 a {
		color: var(--theme-text-primary);
		text-decoration: none;
		transition: color 0.2s ease;
	}

	.article-header h2 a:hover {
		color: var(--theme-accent-primary);
	}

	.article-date {
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
		font-weight: 500;
	}

	.article-excerpt {
		margin: 1rem 0;
	}

	.article-excerpt p {
		color: var(--theme-text-secondary);
		line-height: 1.6;
		margin: 0;
	}

	.article-footer {
		margin-top: 1rem;
	}

	.read-more {
		color: var(--theme-accent-primary);
		text-decoration: none;
		font-weight: 500;
		transition: color 0.2s ease;
	}

	.read-more:hover {
		color: var(--theme-accent-primary-hover);
		text-decoration: underline;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		text-decoration: none;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		transition: background-color 0.2s ease;
	}

	.btn.primary {
		background-color: var(--theme-accent-primary);
		color: white;
	}

	.btn.primary:hover:not(:disabled) {
		background-color: var(--theme-accent-primary-hover);
	}

	/* Responsive design */
	@media (max-width: 768px) {
		.container {
			padding: 0 1rem;
		}

		.page-header {
			padding: 2rem 0 1rem;
		}

		.page-header h1 {
			font-size: 2rem;
		}

		.articles-grid {
			grid-template-columns: 1fr;
			gap: 1.5rem;
		}

		.btn {
			padding: 0.5rem 1rem;
			font-size: 0.9rem;
		}
	}
</style>
