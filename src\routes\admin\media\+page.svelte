<script>
  import { onMount } from 'svelte';

  // Define types for media items
  /** @typedef {{
    id: number;
    originalName: string;
    path: string;
    thumbnailPath?: string;
    type: 'image' | 'video' | 'document';
    mimeType: string;
    size: number;
    alt?: string;
    caption?: string;
    createdAt: string;
    updatedAt: string;
  }} MediaItem */

  // State variables
  /** @type {MediaItem[]} */
  let mediaItems = [];
  let loading = true;
  /** @type {string | null} */
  let error = null;
  /** @type {'all' | 'image' | 'video' | 'document'} */
  let filter = 'all';

  // Fetch media items from the API
  async function fetchMediaItems() {
    try {
      loading = true;
      error = null;

      const response = await fetch('/api/media?limit=100');

      if (!response.ok) {
        throw new Error('Failed to fetch media items');
      }

      const data = await response.json();
      mediaItems = data.data || [];
    } catch (err) {
      const error_msg = err instanceof Error ? err.message : String(err);
      console.error('Error fetching media items:', err);
      error = error_msg || 'Failed to load media items';
    } finally {
      loading = false;
    }
  }

  /**
   * Delete a media item
   * @param {number} id - The ID of the media item to delete
   */
  async function deleteMediaItem(id) {
    if (!confirm('Are you sure you want to delete this media item?')) {
      return;
    }

    try {
      const response = await fetch(`/api/media/${id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to delete media item');
      }

      // Remove the item from the list
      mediaItems = mediaItems.filter(item => item.id !== id);
    } catch (err) {
      const error_msg = err instanceof Error ? err.message : String(err);
      console.error('Error deleting media item:', err);
      alert(error_msg || 'Failed to delete media item');
    }
  }

  // Filter media items by type
  $: filteredItems = filter === 'all'
    ? mediaItems
    : mediaItems.filter(item => item.type === filter);

  /**
   * Handle image loading errors by setting a fallback image
   * @param {Event} event - The error event
   */
  function handleImageError(event) {
    // We know this is an image element
    const img = event.target;
    if (img && 'src' in img) {
      img.src = '/images/placeholder.jpg';
    }
  }

  // Load media items on mount
  onMount(() => {
    fetchMediaItems();
  });
</script>

<svelte:head>
  <title>Media Library - Admin</title>
</svelte:head>

<div class="media-admin-container">
  <div class="header">
    <h1>Media Library</h1>
    <a href="/admin/media/upload" class="btn primary">Upload New Media</a>
  </div>

  {#if error}
    <div class="error-message">
      <p>{error}</p>
      <button class="btn secondary" on:click={fetchMediaItems}>Try Again</button>
    </div>
  {/if}

  <div class="filters">
    <label>
      Filter by type:
      <select bind:value={filter}>
        <option value="all">All Media</option>
        <option value="image">Images</option>
        <option value="video">Videos</option>
        <option value="document">Documents</option>
      </select>
    </label>
  </div>

  {#if loading}
    <div class="loading">
      <p>Loading media items...</p>
    </div>
  {:else if mediaItems.length === 0}
    <div class="empty-state">
      <p>No media items found. Upload some files to get started!</p>
      <a href="/admin/media/upload" class="btn primary">Upload Media</a>
    </div>
  {:else if filteredItems.length === 0}
    <div class="empty-state">
      <p>No media items match the selected filter.</p>
      <button class="btn secondary" on:click={() => filter = 'all'}>Show All Media</button>
    </div>
  {:else}
    <div class="media-grid">
      {#each filteredItems as item}
        <div class="media-item">
          <div class="media-image">
            <!-- Use a function to handle image errors -->
            <img
              src={item.thumbnailPath || item.path || '/images/placeholder.jpg'}
              alt={item.alt || item.originalName}
              on:error={handleImageError}
            />
          </div>
          <div class="media-info">
            <h3 class="media-title">{item.originalName}</h3>
            <p class="media-meta">
              <span class="type">{item.type}</span>
              <span class="size">{Math.round(item.size / 1024)} KB</span>
            </p>
            <p class="date">Uploaded: {new Date(item.createdAt).toLocaleDateString()}</p>
          </div>
          <div class="media-actions">
            <a
              href={`/admin/media/edit/${item.id}`}
              class="btn icon-btn"
              title="Edit"
            >
              ✏️
            </a>
            <button
              class="btn icon-btn delete"
              title="Delete"
              on:click={() => deleteMediaItem(item.id)}
            >
              🗑️
            </button>
          </div>
        </div>
      {/each}
    </div>
  {/if}
</div>

<style>
  .media-admin-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }

  .filters {
    margin-bottom: 1.5rem;
  }

  .filters select {
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #ddd;
    margin-left: 0.5rem;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .primary {
    background-color: #4caf50;
    color: white;
  }

  .secondary {
    background-color: #f0f0f0;
    color: #333;
  }

  .icon-btn {
    padding: 0.5rem;
    font-size: 1.2rem;
    background: none;
  }

  .delete {
    color: #e53935;
  }

  .error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .loading, .empty-state {
    text-align: center;
    padding: 3rem;
    background-color: #f5f5f5;
    border-radius: 4px;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .media-item {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background-color: white;
    position: relative;
  }

  .media-image {
    height: 180px;
    overflow: hidden;
  }

  .media-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .media-info {
    padding: 1rem;
  }

  .media-title {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .media-meta {
    display: flex;
    gap: 1rem;
    margin: 0.5rem 0;
    font-size: 0.9rem;
    color: #666;
  }

  .date {
    font-size: 0.8rem;
    color: #888;
    margin: 0.5rem 0 0 0;
  }

  .media-actions {
    display: flex;
    justify-content: flex-end;
    padding: 0.5rem;
    background-color: #f9f9f9;
    border-top: 1px solid #eee;
  }
</style>
