import { sequence } from '@sveltejs/kit/hooks';
import { json } from '@sveltejs/kit';
import type { <PERSON>le } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import logger from '$lib/server/services/logger';

// Handle authentication
const handleAuth: Handle = async ({ event, resolve }) => {
  const sessionId = event.cookies.get('session_id');

  // For development, we'll check for a session cookie first
  // If no session cookie exists, we won't auto-login as admin
  // This allows us to test the login flow properly
  if (sessionId) {
    try {
      // In a real app, you'd verify the session token and look up the session
      // For now, we'll just assume the session is valid and look up the user

      // For development mode, we'll check if the user exists in the database
      const userResult = await db.select()
        .from(users)
        .where(eq(users.email, '<EMAIL>'))
        .limit(1);

      if (userResult.length > 0) {
        // User exists in database, use that
        const user = userResult[0];
        // Convert database user to the format expected by locals
        event.locals.user = {
          id: user.id,
          username: user.username,
          displayName: user.displayName,
          email: user.email,
          role: user.role,
          preferences: user.preferences || {
            highContrast: false,
            largeText: false,
            simplifiedInterface: false
          }
        };
        logger.debug('User authenticated from database', {
          email: event.locals.user.email,
          role: event.locals.user.role
        });
      } else {
        // For development, create a mock admin user if no user found
        // This simulates having the admin in the database
        event.locals.user = {
          id: 1,
          username: 'admin',
          displayName: 'Admin User',
          email: '<EMAIL>',
          role: 'admin',
          preferences: {
            highContrast: false,
            largeText: false,
            simplifiedInterface: false
          }
        };
        logger.debug('Using mock admin user', { email: '<EMAIL>' });
      }
    } catch (error) {
      logger.error('Error verifying session:', { error });
    }
  } else {
    logger.debug('No session cookie found');
  }

  return resolve(event);
};

// Handle accessibility preferences
const handleAccessibility: Handle = async ({ event, resolve }) => {
  // Get user preferences from the session
  const userPreferences = event.locals.user?.preferences || {
    highContrast: false,
    largeText: false,
    simplifiedInterface: false
  };

  // Add preferences to locals
  event.locals.accessibility = userPreferences;

  // Override with URL parameters if present
  const url = new URL(event.request.url);
  if (url.searchParams.has('highContrast')) {
    event.locals.accessibility.highContrast = url.searchParams.get('highContrast') === 'true';
  }
  if (url.searchParams.has('largeText')) {
    event.locals.accessibility.largeText = url.searchParams.get('largeText') === 'true';
  }
  if (url.searchParams.has('simplifiedInterface')) {
    event.locals.accessibility.simplifiedInterface = url.searchParams.get('simplifiedInterface') === 'true';
  }

  // Resolve with transformed HTML
  const response = await resolve(event);
  return response;
};

// Handle AdminJS routes
const handleAdminJS: Handle = async ({ event, resolve }) => {
  const { pathname } = new URL(event.request.url);

  // Check if the request is for the admin panel API
  if (pathname.startsWith('/admin/api')) {
    // Redirect to the standalone AdminJS server API
    const newUrl = `http://localhost:3001${pathname}`;
    return Response.redirect(newUrl, 307);
  }

  return resolve(event);
};

// Handle errors
const handleErrors: Handle = async ({ event, resolve }) => {
  try {
    const response = await resolve(event);
    return response;
  } catch (error) {
    // Convert unknown error to a typed error
    const err = error as Error;

    // Log the error
    logger.error(`Server Error: ${err.message || 'Unknown error'}`, {
      error: err,
      url: event.url.pathname,
      method: event.request.method,
      source: 'server'
    });

    // Return a JSON error response for API routes
    if (event.url.pathname.startsWith('/api/')) {
      return json({
        success: false,
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? err.message : undefined
      }, { status: 500 });
    }

    // For regular routes, throw the error to let SvelteKit handle it
    throw error;
  }
};

// Export the sequence of handlers
export const handle = sequence(handleAuth, handleAccessibility, handleAdminJS, handleErrors);
