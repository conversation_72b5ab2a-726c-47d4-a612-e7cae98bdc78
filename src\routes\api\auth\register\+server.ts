import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';

// POST /api/auth/register - Register a new user
export const POST: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.username || !body.displayName || !body.email || !body.password) {
      return json({
        success: false,
        error: 'Username, display name, email, and password are required'
      }, { status: 400 });
    }
    
    // Check if username or email already exists
    const existingUser = await db.select()
      .from(users)
      .where(eq(users.username, body.username))
      .or(eq(users.email, body.email))
      .limit(1);
    
    if (existingUser.length > 0) {
      return json({
        success: false,
        error: 'Username or email already exists'
      }, { status: 409 });
    }
    
    // In a real app, you'd hash the password here
    // For now, we'll just store it as is for simplicity
    const passwordHash = body.password;
    
    // Insert new user into the database
    const result = await db.insert(users).values({
      username: body.username,
      displayName: body.displayName,
      email: body.email,
      passwordHash,
      role: 'user', // Default role
      preferences: {
        highContrast: false,
        largeText: false,
        simplifiedInterface: false
      }
    }).returning();
    
    // Return user data (excluding sensitive information)
    const { passwordHash: _, ...userData } = result[0];
    
    return json({
      success: true,
      data: userData
    }, { status: 201 });
  } catch (error) {
    console.error('Error registering user:', error);
    return json({
      success: false,
      error: 'Failed to register user'
    }, { status: 500 });
  }
};
