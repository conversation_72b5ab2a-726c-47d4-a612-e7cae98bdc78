<script>
	import { onMount } from 'svelte';
	
	// Message board data
	let messages = [
		{
			id: 1,
			username: '<PERSON><PERSON><PERSON><PERSON>',
			content: 'Just rewatched Stranger Things Season 1 and <PERSON>\'s performance was amazing! Anyone else think <PERSON> is the heart of the show?',
			date: '2025-05-10T14:30:00',
			likes: 24,
			replies: [
				{
					id: 101,
					username: '<PERSON><PERSON><PERSON>',
					content: 'Absolutely! <PERSON> is the glue that holds the group together. <PERSON> brings so much authenticity to the role.',
					date: '2025-05-10T15:45:00',
					likes: 12
				},
				{
					id: 102,
					username: 'Eleven<PERSON><PERSON>',
					content: 'I think all the kids are great, but <PERSON> definitely stands out in the early seasons. His chemistry with <PERSON> is incredible.',
					date: '2025-05-10T16:20:00',
					likes: 8
				}
			]
		},
		{
			id: 2,
			username: '<PERSON><PERSON><PERSON><PERSON>',
			content: 'Has anyone listened to The Aubreys\' latest single? It\'s been on repeat for me all week!',
			date: '2025-05-09T10:15:00',
			likes: 18,
			replies: [
				{
					id: 201,
					username: '<PERSON><PERSON>over99',
					content: 'Yes! It\'s so good. I love how <PERSON>\'s musical style has evolved over the years.',
					date: '2025-05-09T11:30:00',
					likes: 7
				}
			]
		},
		{
			id: 3,
			username: 'Film<PERSON>uff2000',
			content: '<PERSON> saw <PERSON>\'s new movie and was blown away. He\'s really growing as an actor and taking on more complex roles. What\'s your favorite <PERSON> <PERSON>hard movie?',
			date: '2025-05-08T20:45:00',
			likes: 31,
			replies: [
				{
					id: 301,
					username: 'HorrorFan666',
					content: 'IT is still my favorite. His portrayal of <PERSON> Tozier was perfect - funny but with real depth.',
					date: '2025-05-08T21:10:00',
					likes: 15
				},
				{
					id: 302,
					username: 'GhostbustersFan',
					content: 'Ghostbusters: Afterlife for me! He brought something fresh to the franchise while honoring its legacy.',
					date: '2025-05-08T22:05:00',
					likes: 9
				},
				{
					id: 303,
					username: 'CinemaExpert',
					content: 'The Goldfinch is underrated. It wasn\'t a perfect film, but Finn\'s performance was really nuanced.',
					date: '2025-05-09T08:30:00',
					likes: 6
				}
			]
		},
		{
			id: 4,
			username: 'DirectorDreamer',
			content: 'Did anyone catch Finn\'s interview where he talked about wanting to direct more? I\'m so excited to see what he does behind the camera!',
			date: '2025-05-07T16:20:00',
			likes: 27,
			replies: []
		}
	];
	
	// New message form
	let newMessageContent = '';
	let replyContent = '';
	let replyingToId = null;
	
	// User info (in a real app, this would come from authentication)
	const currentUser = {
		username: 'FinnFan2025',
		isLoggedIn: true
	};
	
	// Handle new message submission
	function handleSubmitMessage() {
		if (!newMessageContent.trim()) return;
		
		const newMessage = {
			id: messages.length + 1,
			username: currentUser.username,
			content: newMessageContent,
			date: new Date().toISOString(),
			likes: 0,
			replies: []
		};
		
		messages = [newMessage, ...messages];
		newMessageContent = '';
	}
	
	// Handle reply submission
	function handleSubmitReply(messageId) {
		if (!replyContent.trim()) return;
		
		const newReply = {
			id: Math.floor(Math.random() * 10000),
			username: currentUser.username,
			content: replyContent,
			date: new Date().toISOString(),
			likes: 0
		};
		
		messages = messages.map(message => {
			if (message.id === messageId) {
				return {
					...message,
					replies: [...message.replies, newReply]
				};
			}
			return message;
		});
		
		replyContent = '';
		replyingToId = null;
	}
	
	// Handle like
	function handleLike(messageId, replyId = null) {
		if (replyId === null) {
			// Like a message
			messages = messages.map(message => {
				if (message.id === messageId) {
					return {
						...message,
						likes: message.likes + 1
					};
				}
				return message;
			});
		} else {
			// Like a reply
			messages = messages.map(message => {
				if (message.id === messageId) {
					return {
						...message,
						replies: message.replies.map(reply => {
							if (reply.id === replyId) {
								return {
									...reply,
									likes: reply.likes + 1
								};
							}
							return reply;
						})
					};
				}
				return message;
			});
		}
	}
	
	// Format date
	function formatDate(dateString) {
		const date = new Date(dateString);
		return date.toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}
</script>

<svelte:head>
	<title>Message Board - Finn Wolfhard Fan Club</title>
	<meta name="description" content="Join the conversation with other Finn Wolfhard fans on our message board" />
</svelte:head>

<div class="message-board-container">
	<h1>Message Board</h1>
	
	<div class="board-description">
		<p>Welcome to the Finn Wolfhard Fan Club message board! This is a place for fans to discuss Finn's projects, share thoughts, and connect with other fans. Please be respectful and follow our <a href="/community-guidelines">community guidelines</a>.</p>
	</div>
	
	{#if currentUser.isLoggedIn}
		<div class="new-message-form">
			<h2>Post a Message</h2>
			<div class="form-group">
				<textarea 
					bind:value={newMessageContent}
					placeholder="Share your thoughts with other fans..."
					rows="4"
				></textarea>
			</div>
			<button class="btn primary" on:click={handleSubmitMessage}>Post Message</button>
		</div>
	{:else}
		<div class="login-prompt">
			<p>Please <a href="/login">log in</a> or <a href="/register">register</a> to participate in the message board.</p>
		</div>
	{/if}
	
	<div class="messages-container">
		{#each messages as message}
			<div class="message-card">
				<div class="message-header">
					<span class="username">{message.username}</span>
					<span class="date">{formatDate(message.date)}</span>
				</div>
				<div class="message-content">
					<p>{message.content}</p>
				</div>
				<div class="message-actions">
					<button class="action-btn" on:click={() => handleLike(message.id)}>
						<span class="like-icon">♥</span> {message.likes}
					</button>
					<button class="action-btn" on:click={() => replyingToId = replyingToId === message.id ? null : message.id}>
						Reply
					</button>
				</div>
				
				{#if replyingToId === message.id && currentUser.isLoggedIn}
					<div class="reply-form">
						<textarea 
							bind:value={replyContent}
							placeholder="Write your reply..."
							rows="3"
						></textarea>
						<div class="reply-actions">
							<button class="btn secondary" on:click={() => replyingToId = null}>Cancel</button>
							<button class="btn primary" on:click={() => handleSubmitReply(message.id)}>Submit Reply</button>
						</div>
					</div>
				{/if}
				
				{#if message.replies.length > 0}
					<div class="replies-container">
						{#each message.replies as reply}
							<div class="reply-card">
								<div class="reply-header">
									<span class="username">{reply.username}</span>
									<span class="date">{formatDate(reply.date)}</span>
								</div>
								<div class="reply-content">
									<p>{reply.content}</p>
								</div>
								<div class="reply-actions">
									<button class="action-btn" on:click={() => handleLike(message.id, reply.id)}>
										<span class="like-icon">♥</span> {reply.likes}
									</button>
								</div>
							</div>
						{/each}
					</div>
				{/if}
			</div>
		{/each}
	</div>
</div>

<style>
	.message-board-container {
		max-width: 900px;
		margin: 0 auto;
		padding: 0 1rem;
	}
	
	h1 {
		text-align: center;
		margin-bottom: 1.5rem;
	}
	
	.board-description {
		background-color: var(--color-bg-2);
		padding: 1rem;
		border-radius: 8px;
		margin-bottom: 2rem;
	}
	
	.new-message-form, .login-prompt {
		background-color: white;
		padding: 1.5rem;
		border-radius: 8px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		margin-bottom: 2rem;
	}
	
	.new-message-form h2 {
		margin-top: 0;
		margin-bottom: 1rem;
		font-size: 1.5rem;
	}
	
	.form-group {
		margin-bottom: 1rem;
	}
	
	textarea {
		width: 100%;
		padding: 0.8rem;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-family: inherit;
		font-size: 1rem;
		resize: vertical;
	}
	
	.btn {
		display: inline-block;
		padding: 0.8rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-weight: bold;
		cursor: pointer;
		transition: all 0.3s ease;
	}
	
	.btn.primary {
		background-color: var(--color-theme-1);
		color: white;
	}
	
	.btn.primary:hover {
		background-color: var(--color-theme-2);
	}
	
	.btn.secondary {
		background-color: #f0f0f0;
		color: #333;
	}
	
	.btn.secondary:hover {
		background-color: #e0e0e0;
	}
	
	.login-prompt {
		text-align: center;
	}
	
	.messages-container {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}
	
	.message-card {
		background-color: white;
		border-radius: 8px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		overflow: hidden;
	}
	
	.message-header, .reply-header {
		display: flex;
		justify-content: space-between;
		padding: 1rem;
		background-color: #f8f8f8;
		border-bottom: 1px solid #eee;
	}
	
	.username {
		font-weight: bold;
		color: var(--color-theme-1);
	}
	
	.date {
		color: #666;
		font-size: 0.9rem;
	}
	
	.message-content, .reply-content {
		padding: 1rem;
	}
	
	.message-content p, .reply-content p {
		margin: 0;
		line-height: 1.5;
	}
	
	.message-actions, .reply-actions {
		display: flex;
		padding: 0.5rem 1rem;
		border-top: 1px solid #eee;
	}
	
	.action-btn {
		background: none;
		border: none;
		color: #666;
		cursor: pointer;
		padding: 0.5rem;
		margin-right: 1rem;
		font-size: 0.9rem;
		display: flex;
		align-items: center;
	}
	
	.action-btn:hover {
		color: var(--color-theme-1);
	}
	
	.like-icon {
		margin-right: 0.3rem;
	}
	
	.reply-form {
		padding: 1rem;
		background-color: #f8f8f8;
		border-top: 1px solid #eee;
	}
	
	.reply-form textarea {
		margin-bottom: 1rem;
	}
	
	.reply-actions {
		display: flex;
		justify-content: flex-end;
		gap: 0.5rem;
	}
	
	.replies-container {
		padding: 0 1rem 1rem 2rem;
		border-top: 1px solid #eee;
	}
	
	.reply-card {
		background-color: #f8f8f8;
		border-radius: 4px;
		margin-top: 1rem;
		border: 1px solid #eee;
	}
	
	.reply-header {
		background-color: #f0f0f0;
	}
</style>
