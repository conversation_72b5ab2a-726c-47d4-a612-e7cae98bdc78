<script>
	// Gallery data
	const galleryItems = [
		{
			id: 1,
			title: 'On the Set of Stranger Things',
			description: '<PERSON> with the cast of Stranger Things during filming of Season 4.',
			imageUrl: '/images/gallery-1.jpg',
			thumbnailUrl: '/images/gallery-1-thumb.jpg',
			category: 'tv',
			date: '2022-05-15'
		},
		{
			id: 2,
			title: 'The Aubreys Live Performance',
			description: '<PERSON> performing with his band The Aubreys at a live concert in Los Angeles.',
			imageUrl: '/images/gallery-2.jpg',
			thumbnailUrl: '/images/gallery-2-thumb.jpg',
			category: 'music',
			date: '2023-02-10'
		},
		{
			id: 3,
			title: 'Red Carpet Appearance',
			description: '<PERSON> at the premiere of his latest film in New York City.',
			imageUrl: '/images/gallery-3.jpg',
			thumbnailUrl: '/images/gallery-3-thumb.jpg',
			category: 'events',
			date: '2023-04-22'
		},
		{
			id: 4,
			title: 'Behind the Scenes',
			description: 'Behind the scenes photo from the set of Ghostbusters: Afterlife.',
			imageUrl: '/images/gallery-4.jpg',
			thumbnailUrl: '/images/gallery-4-thumb.jpg',
			category: 'movies',
			date: '2021-11-05'
		},
		{
			id: 5,
			title: 'Fan Convention',
			description: '<PERSON> meeting fans at Comic-Con 2022.',
			imageUrl: '/images/gallery-5.jpg',
			thumbnailUrl: '/images/gallery-5-thumb.jpg',
			category: 'events',
			date: '2022-07-23'
		},
		{
			id: 6,
			title: 'Magazine Photoshoot',
			description: 'Finn during a photoshoot for GQ Magazine.',
			imageUrl: '/images/gallery-6.jpg',
			thumbnailUrl: '/images/gallery-6-thumb.jpg',
			category: 'photoshoots',
			date: '2023-01-15'
		},
		{
			id: 7,
			title: 'Directing Debut',
			description: 'Finn on the set of his directorial debut "Night Shifts".',
			imageUrl: '/images/gallery-7.jpg',
			thumbnailUrl: '/images/gallery-7-thumb.jpg',
			category: 'directing',
			date: '2020-09-18'
		},
		{
			id: 8,
			title: 'Award Ceremony',
			description: 'Finn accepting an award with the cast of Stranger Things.',
			imageUrl: '/images/gallery-8.jpg',
			thumbnailUrl: '/images/gallery-8-thumb.jpg',
			category: 'events',
			date: '2022-01-30'
		}
	];
	
	// Filter state
	let selectedCategory = 'all';
	let searchQuery = '';
	
	// Computed filtered gallery items
	$: filteredGallery = galleryItems.filter(item => {
		// Filter by category
		const categoryMatch = selectedCategory === 'all' || item.category === selectedCategory;
		
		// Filter by search query
		const searchMatch = searchQuery === '' || 
			item.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
			item.description.toLowerCase().includes(searchQuery.toLowerCase());
		
		return categoryMatch && searchMatch;
	});
	
	// Categories for filter
	const categories = [
		{ id: 'all', name: 'All Categories' },
		{ id: 'tv', name: 'TV Shows' },
		{ id: 'movies', name: 'Movies' },
		{ id: 'music', name: 'Music' },
		{ id: 'events', name: 'Events' },
		{ id: 'photoshoots', name: 'Photoshoots' },
		{ id: 'directing', name: 'Directing' }
	];
	
	// Handle category change
	function handleCategoryChange(event) {
		selectedCategory = event.target.value;
	}
	
	// Handle search input
	function handleSearchInput(event) {
		searchQuery = event.target.value;
	}
</script>

<svelte:head>
	<title>Gallery - Finn Wolfhard Fan Club</title>
	<meta name="description" content="Photo gallery of Finn Wolfhard from movies, TV shows, music performances, and events" />
</svelte:head>

<div class="gallery-container">
	<h1>Photo Gallery</h1>
	
	<div class="gallery-filters">
		<div class="search-box">
			<input 
				type="text" 
				placeholder="Search gallery..." 
				value={searchQuery}
				on:input={handleSearchInput}
			/>
		</div>
		
		<div class="category-filter">
			<select value={selectedCategory} on:change={handleCategoryChange}>
				{#each categories as category}
					<option value={category.id}>{category.name}</option>
				{/each}
			</select>
		</div>
	</div>
	
	{#if filteredGallery.length === 0}
		<div class="no-results">
			<p>No gallery items match your search criteria. Please try a different search or category.</p>
		</div>
	{:else}
		<div class="gallery-grid">
			{#each filteredGallery as item}
				<div class="gallery-item">
					<a href={`/gallery/${item.id}`}>
						<div class="gallery-image">
							<img src={item.thumbnailUrl} alt={item.title} />
						</div>
						<div class="gallery-info">
							<h3>{item.title}</h3>
							<p class="date">{item.date}</p>
							<p class="description">{item.description}</p>
						</div>
					</a>
				</div>
			{/each}
		</div>
	{/if}
</div>

<style>
	.gallery-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 1rem;
	}
	
	h1 {
		text-align: center;
		margin-bottom: 2rem;
	}
	
	.gallery-filters {
		display: flex;
		flex-direction: column;
		gap: 1rem;
		margin-bottom: 2rem;
	}
	
	.search-box input {
		width: 100%;
		padding: 0.8rem;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 1rem;
	}
	
	.category-filter select {
		width: 100%;
		padding: 0.8rem;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 1rem;
		background-color: white;
	}
	
	.gallery-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
		gap: 1.5rem;
	}
	
	.gallery-item {
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
		transition: transform 0.3s ease;
	}
	
	.gallery-item:hover {
		transform: translateY(-5px);
	}
	
	.gallery-item a {
		text-decoration: none;
		color: inherit;
	}
	
	.gallery-image {
		height: 200px;
		overflow: hidden;
	}
	
	.gallery-image img {
		width: 100%;
		height: 100%;
		object-fit: cover;
		transition: transform 0.3s ease;
	}
	
	.gallery-item:hover .gallery-image img {
		transform: scale(1.05);
	}
	
	.gallery-info {
		padding: 1rem;
	}
	
	.gallery-info h3 {
		margin-top: 0;
		margin-bottom: 0.5rem;
		font-size: 1.2rem;
	}
	
	.date {
		color: #666;
		font-size: 0.9rem;
		margin-bottom: 0.5rem;
	}
	
	.description {
		font-size: 0.9rem;
		line-height: 1.4;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
	
	.no-results {
		text-align: center;
		padding: 2rem;
		background-color: #f8f8f8;
		border-radius: 8px;
	}
	
	@media (min-width: 768px) {
		.gallery-filters {
			flex-direction: row;
			justify-content: space-between;
		}
		
		.search-box {
			flex: 1;
			margin-right: 1rem;
		}
		
		.category-filter {
			width: 250px;
		}
	}
</style>
