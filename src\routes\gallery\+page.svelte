<script>
	import { onMount } from 'svelte';

	// State variables
	/** @type {Array<{id: number, title: string, description?: string, imageUrl: string, thumbnailUrl: string, createdAt: string, published: boolean, category?: string}>} */
	let galleryItems = [];
	let loading = true;
	/** @type {string|null} */
	let error = null;

	// Load gallery items when component mounts
	onMount(() => {
		fetchGalleryItems();
	});

	// Fetch gallery items from the API
	async function fetchGalleryItems() {
		try {
			loading = true;
			error = null;

			const response = await fetch('/api/gallery?limit=100');

			if (!response.ok) {
				throw new Error('Failed to fetch gallery items');
			}

			const data = await response.json();

			if (data.success) {
				galleryItems = data.data || [];
			} else {
				throw new Error(data.error || 'Failed to fetch gallery items');
			}
		} catch (err) {
			console.error('Error fetching gallery items:', err);
			error = err instanceof Error ? err.message : 'Failed to load gallery items';
			// Fallback to hardcoded data if API fails
			galleryItems = [
				{
					id: 1,
					title: 'On the Set of Stranger Things',
					description: '<PERSON> with the cast of Stranger Things during filming of Season 4.',
					imageUrl: '/images/gallery-1.jpg',
					thumbnailUrl: '/images/gallery-1-thumb.jpg',
					createdAt: '2022-05-15',
					published: true
				},
				{
					id: 2,
					title: 'The Aubreys Live Performance',
					description: 'Finn performing with his band The Aubreys at a live concert in Los Angeles.',
					imageUrl: '/images/gallery-2.jpg',
					thumbnailUrl: '/images/gallery-2-thumb.jpg',
					createdAt: '2023-02-10',
					published: true
				},
				{
					id: 3,
					title: 'Red Carpet Appearance',
					description: 'Finn at the premiere of his latest film in New York City.',
					imageUrl: '/images/gallery-3.jpg',
					thumbnailUrl: '/images/gallery-3-thumb.jpg',
					createdAt: '2023-04-22',
					published: true
				},
				{
					id: 4,
					title: 'Behind the Scenes',
					description: 'Behind the scenes photo from the set of Ghostbusters: Afterlife.',
					imageUrl: '/images/gallery-4.jpg',
					thumbnailUrl: '/images/gallery-4-thumb.jpg',
					createdAt: '2021-11-05',
					published: true
				},
				{
					id: 5,
					title: 'Fan Convention',
					description: 'Finn meeting fans at Comic-Con 2022.',
					imageUrl: '/images/gallery-5.jpg',
					thumbnailUrl: '/images/gallery-5-thumb.jpg',
					createdAt: '2022-07-23',
					published: true
				},
				{
					id: 6,
					title: 'Magazine Photoshoot',
					description: 'Finn during a photoshoot for GQ Magazine.',
					imageUrl: '/images/gallery-6.jpg',
					thumbnailUrl: '/images/gallery-6-thumb.jpg',
					createdAt: '2023-01-15',
					published: true
				},
				{
					id: 7,
					title: 'Directing Debut',
					description: 'Finn on the set of his directorial debut "Night Shifts".',
					imageUrl: '/images/gallery-7.jpg',
					thumbnailUrl: '/images/gallery-7-thumb.jpg',
					createdAt: '2020-09-18',
					published: true
				},
				{
					id: 8,
					title: 'Award Ceremony',
					description: 'Finn accepting an award with the cast of Stranger Things.',
					imageUrl: '/images/gallery-8.jpg',
					thumbnailUrl: '/images/gallery-8-thumb.jpg',
					createdAt: '2022-01-30',
					published: true
				}
			];
		} finally {
			loading = false;
		}
	}

	// Filter state
	let selectedCategory = 'all';
	let searchQuery = '';

	// Computed filtered gallery items
	$: filteredGallery = galleryItems.filter(item => {
		// Filter by category
		const categoryMatch = selectedCategory === 'all' || item.category === selectedCategory;

		// Filter by search query
		const searchMatch = searchQuery === '' ||
			item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
			(item.description && item.description.toLowerCase().includes(searchQuery.toLowerCase()));

		return categoryMatch && searchMatch;
	});

	// Categories for filter
	const categories = [
		{ id: 'all', name: 'All Categories' },
		{ id: 'tv', name: 'TV Shows' },
		{ id: 'movies', name: 'Movies' },
		{ id: 'music', name: 'Music' },
		{ id: 'events', name: 'Events' },
		{ id: 'photoshoots', name: 'Photoshoots' },
		{ id: 'directing', name: 'Directing' }
	];

	// Handle category change
	function handleCategoryChange(event) {
		selectedCategory = event.target.value;
	}

	// Handle search input
	function handleSearchInput(event) {
		searchQuery = event.target.value;
	}
</script>

<svelte:head>
	<title>Gallery - Finn Wolfhard Fan Club</title>
	<meta name="description" content="Photo gallery of Finn Wolfhard from movies, TV shows, music performances, and events" />
</svelte:head>

<div class="gallery-container">
	<h1>Photo Gallery</h1>

	<div class="gallery-filters">
		<div class="search-box">
			<input
				type="text"
				placeholder="Search gallery..."
				value={searchQuery}
				on:input={handleSearchInput}
			/>
		</div>

		<div class="category-filter">
			<select value={selectedCategory} on:change={handleCategoryChange}>
				{#each categories as category}
					<option value={category.id}>{category.name}</option>
				{/each}
			</select>
		</div>
	</div>

	{#if loading}
		<div class="loading">
			<p>Loading gallery items...</p>
		</div>
	{:else if error}
		<div class="error">
			<p>Error: {error}</p>
			<p>Showing fallback content.</p>
		</div>
	{/if}

	{#if !loading && filteredGallery.length === 0}
		<div class="no-results">
			<p>No gallery items match your search criteria. Please try a different search or category.</p>
		</div>
	{:else if !loading}
		<div class="gallery-grid">
			{#each filteredGallery as item}
				<div class="gallery-item">
					<a href={`/gallery/${item.id}`}>
						<div class="gallery-image">
							<img src={item.thumbnailUrl} alt={item.title} />
						</div>
						<div class="gallery-info">
							<h3>{item.title}</h3>
							<p class="date">{new Date(item.createdAt).toLocaleDateString()}</p>
							<p class="description">{item.description || ''}</p>
						</div>
					</a>
				</div>
			{/each}
		</div>
	{/if}
</div>

<style>
	.gallery-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 1rem;
	}

	h1 {
		text-align: center;
		margin-bottom: 2rem;
	}

	.gallery-filters {
		display: flex;
		flex-direction: column;
		gap: 1rem;
		margin-bottom: 2rem;
	}

	.search-box input {
		width: 100%;
		padding: 0.8rem;
		border: 1px solid var(--theme-input-border);
		border-radius: 4px;
		font-size: 1rem;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
	}

	.search-box input:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
	}

	.category-filter select {
		width: 100%;
		padding: 0.8rem;
		border: 1px solid var(--theme-input-border);
		border-radius: 4px;
		font-size: 1rem;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
	}

	.category-filter select:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
	}

	.gallery-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
		gap: 1.5rem;
	}

	.gallery-item {
		background: var(--theme-card-bg);
		border: 1px solid var(--theme-card-border);
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 4px 6px var(--theme-shadow);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.gallery-item:hover {
		transform: translateY(-5px);
		box-shadow: 0 8px 15px var(--theme-shadow-hover);
	}

	.gallery-item a {
		text-decoration: none;
		color: inherit;
	}

	.gallery-image {
		height: 200px;
		overflow: hidden;
	}

	.gallery-image img {
		width: 100%;
		height: 100%;
		object-fit: cover;
		transition: transform 0.3s ease;
	}

	.gallery-item:hover .gallery-image img {
		transform: scale(1.05);
	}

	.gallery-info {
		padding: 1rem;
		background: var(--theme-card-bg);
		color: var(--theme-text-primary);
	}

	.gallery-info h3 {
		margin-top: 0;
		margin-bottom: 0.5rem;
		font-size: 1.2rem;
		color: var(--theme-text-primary);
	}

	.date {
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
		margin-bottom: 0.5rem;
	}

	.description {
		font-size: 0.9rem;
		line-height: 1.4;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.no-results, .loading, .error {
		text-align: center;
		padding: 2rem;
		background-color: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		margin-bottom: 2rem;
		color: var(--theme-text-primary);
	}

	.error {
		background-color: var(--theme-accent-danger);
		color: white;
		border-color: var(--theme-accent-danger);
	}

	@media (min-width: 768px) {
		.gallery-filters {
			flex-direction: row;
			justify-content: space-between;
		}

		.search-box {
			flex: 1;
			margin-right: 1rem;
		}

		.category-filter {
			width: 250px;
		}
	}
</style>
