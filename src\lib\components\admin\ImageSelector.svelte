<script>
	import { createEventDispatcher } from 'svelte';
	import GalleryPicker from './GalleryPicker.svelte';
	import ImageUploader from './ImageUploader.svelte';

	const dispatch = createEventDispatcher();

	// Props
	export let selectedImageUrl = '';
	export let disabled = false;

	// State
	let activeTab = 'gallery'; // 'gallery' or 'upload'
	let showGalleryPicker = false;
	let previewImage = selectedImageUrl;

	// Handle gallery selection
	function handleGallerySelect(event) {
		const { imageUrl, thumbnailUrl, title, galleryId } = event.detail;
		previewImage = imageUrl;
		dispatch('select', {
			imageUrl,
			thumbnailUrl,
			source: 'gallery',
			galleryId,
			title
		});
	}

	// Handle upload completion
	function handleUploadComplete(event) {
		const { imageUrl, thumbnailUrl, filename } = event.detail;
		previewImage = imageUrl;
		dispatch('select', {
			imageUrl,
			thumbnailUrl,
			source: 'upload',
			filename
		});
	}

	// Clear selection
	function clearSelection() {
		previewImage = '';
		dispatch('select', {
			imageUrl: '',
			thumbnailUrl: '',
			source: 'clear'
		});
	}

	// Open gallery picker
	function openGalleryPicker() {
		showGalleryPicker = true;
	}

	// Update preview when selectedImageUrl changes
	$: previewImage = selectedImageUrl;
</script>

<div class="image-selector">
	<div class="selector-header">
		<h3>Select Hero Image</h3>
		{#if previewImage}
			<button class="btn secondary small" on:click={clearSelection}>
				Clear Selection
			</button>
		{/if}
	</div>

	{#if previewImage}
		<!-- Preview Section -->
		<div class="preview-section">
			<div class="preview-image">
				<img src={previewImage} alt="Selected hero image" />
			</div>
			<div class="preview-actions">
				<button class="btn secondary" on:click={openGalleryPicker} {disabled}>
					Choose Different Image
				</button>
				<button class="btn secondary" on:click={() => activeTab = 'upload'} {disabled}>
					Upload New Image
				</button>
			</div>
		</div>
	{:else}
		<!-- Selection Tabs -->
		<div class="tab-container">
			<div class="tab-buttons">
				<button 
					class="tab-btn"
					class:active={activeTab === 'gallery'}
					on:click={() => activeTab = 'gallery'}
					{disabled}
				>
					📷 Choose from Gallery
				</button>
				<button 
					class="tab-btn"
					class:active={activeTab === 'upload'}
					on:click={() => activeTab = 'upload'}
					{disabled}
				>
					📁 Upload New Image
				</button>
			</div>

			<div class="tab-content">
				{#if activeTab === 'gallery'}
					<div class="gallery-tab">
						<p class="tab-description">
							Select an existing image from your gallery
						</p>
						<button 
							class="btn primary"
							on:click={openGalleryPicker}
							{disabled}
						>
							Browse Gallery Images
						</button>
					</div>
				{:else if activeTab === 'upload'}
					<div class="upload-tab">
						<p class="tab-description">
							Upload a new image for your hero section
						</p>
						<ImageUploader 
							on:upload={handleUploadComplete}
							{disabled}
						/>
					</div>
				{/if}
			</div>
		</div>
	{/if}
</div>

<!-- Gallery Picker Modal -->
<GalleryPicker 
	bind:isOpen={showGalleryPicker}
	{selectedImageUrl}
	on:select={handleGallerySelect}
	on:close={() => showGalleryPicker = false}
/>

<style>
	.image-selector {
		width: 100%;
		border: 1px solid #ddd;
		border-radius: 8px;
		overflow: hidden;
		background: white;
	}

	.selector-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1rem;
		background-color: #f8f8f8;
		border-bottom: 1px solid #ddd;
	}

	.selector-header h3 {
		margin: 0;
		font-size: 1.1rem;
	}

	.preview-section {
		padding: 1rem;
	}

	.preview-image {
		height: 200px;
		border-radius: 4px;
		overflow: hidden;
		background-color: #f5f5f5;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 1rem;
	}

	.preview-image img {
		max-width: 100%;
		max-height: 100%;
		object-fit: contain;
	}

	.preview-actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
	}

	.tab-container {
		padding: 1rem;
	}

	.tab-buttons {
		display: flex;
		border-bottom: 1px solid #ddd;
		margin-bottom: 1rem;
	}

	.tab-btn {
		flex: 1;
		padding: 1rem;
		border: none;
		background: none;
		cursor: pointer;
		border-bottom: 2px solid transparent;
		transition: all 0.2s ease;
		font-size: 1rem;
	}

	.tab-btn:hover:not(:disabled) {
		background-color: #f5f5f5;
	}

	.tab-btn.active {
		border-bottom-color: #4caf50;
		color: #4caf50;
		font-weight: bold;
	}

	.tab-btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.tab-content {
		min-height: 200px;
	}

	.gallery-tab, .upload-tab {
		text-align: center;
	}

	.tab-description {
		margin: 0 0 1.5rem 0;
		color: #666;
		font-size: 0.95rem;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		transition: opacity 0.2s ease;
		text-decoration: none;
		display: inline-flex;
		align-items: center;
		justify-content: center;
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.btn.primary {
		background-color: #4caf50;
		color: white;
	}

	.btn.secondary {
		background-color: #f0f0f0;
		color: #333;
	}

	.btn.small {
		padding: 0.5rem 1rem;
		font-size: 0.9rem;
	}

	.btn:hover:not(:disabled) {
		opacity: 0.9;
	}
</style>
