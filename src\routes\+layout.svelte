<script lang="ts">
	import Header from './Header.svelte';
	import '../app.css';
	import { page } from '$app/stores';
	import { onMount } from 'svelte';

	let { children } = $props();
	let highContrast = $state(false);
	let largeText = $state(false);
	let simplifiedInterface = $state(false);
	let showDebugPanel = $state(false);

	function handleKeyDown(event: KeyboardEvent) {
		if (event.ctrlKey && event.shiftKey && event.key === 'D') {
			event.preventDefault();
			showDebugPanel = !showDebugPanel;
		}
	}

	$effect(() => {
		if ($page.data.accessibility) {
			highContrast = $page.data.accessibility.highContrast;
			largeText = $page.data.accessibility.largeText;
			simplifiedInterface = $page.data.accessibility.simplifiedInterface;
		}
	});

	$effect(() => {
		if (typeof document !== 'undefined') {
			if (highContrast) {
				document.body.classList.add('high-contrast');
			} else {
				document.body.classList.remove('high-contrast');
			}

			if (largeText) {
				document.body.classList.add('large-text');
			} else {
				document.body.classList.remove('large-text');
			}

			if (simplifiedInterface) {
				document.body.classList.add('simplified-interface');
			} else {
				document.body.classList.remove('simplified-interface');
			}
		}
	});

	onMount(() => {
		window.addEventListener('keydown', handleKeyDown);

		return () => {
			window.removeEventListener('keydown', handleKeyDown);
		};
	});
</script>

<svelte:head>
	<title>Finn Wolfhard Fan Club</title>
	<meta name="description" content="Official fan club for Finn Wolfhard" />
</svelte:head>

<div class="app" class:high-contrast={highContrast} class:large-text={largeText} class:simplified-interface={simplifiedInterface}>
	<Header />

	<main>
		{@render children()}
	</main>

	<footer>
		<div class="accessibility-controls">
			<button onclick={() => highContrast = !highContrast} class:active={highContrast}>
				High Contrast
			</button>
			<button onclick={() => largeText = !largeText} class:active={largeText}>
				Large Text
			</button>
			<button onclick={() => simplifiedInterface = !simplifiedInterface} class:active={simplifiedInterface}>
				Simplified Interface
			</button>
		</div>
		<p>
			&copy; 2025 Finn Wolfhard Fan Club | <a href="/about">About</a> | <a href="/privacy">Privacy</a>
		</p>
	</footer>

	{#if showDebugPanel}
		<div class="debug-panel">
			<h3>Debug Panel</h3>
			<p>Press Ctrl+Shift+D to close</p>
			<div class="debug-info">
				<p>High Contrast: {highContrast ? 'On' : 'Off'}</p>
				<p>Large Text: {largeText ? 'On' : 'Off'}</p>
				<p>Simplified Interface: {simplifiedInterface ? 'On' : 'Off'}</p>
				<p>Current Route: {$page.url.pathname}</p>
				<p>User: {$page.data.user ? $page.data.user.displayName : 'Not logged in'}</p>
			</div>
		</div>
	{/if}
</div>

<style>
	.app {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
	}

	main {
		flex: 1;
		display: flex;
		flex-direction: column;
		padding: 1rem;
		width: 100%;
		max-width: 64rem;
		margin: 0 auto;
		box-sizing: border-box;
	}

	footer {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 12px;
		background-color: #f5f5f5;
		border-top: 1px solid #e0e0e0;
	}

	footer a {
		font-weight: bold;
		color: var(--color-theme-1);
	}

	.accessibility-controls {
		display: flex;
		gap: 10px;
		margin-bottom: 10px;
	}

	.accessibility-controls button {
		background-color: #f0f0f0;
		border: 1px solid #ccc;
		padding: 5px 10px;
		border-radius: 4px;
		cursor: pointer;
	}

	.accessibility-controls button.active {
		background-color: var(--color-theme-1);
		color: white;
	}

	.debug-panel {
		position: fixed;
		bottom: 20px;
		right: 20px;
		background-color: rgba(0, 0, 0, 0.8);
		color: white;
		padding: 15px;
		border-radius: 5px;
		z-index: 1000;
		max-width: 300px;
	}

	.debug-panel h3 {
		margin-top: 0;
	}

	.debug-info {
		font-family: monospace;
		font-size: 0.9rem;
	}

	/* Accessibility styles */
	:global(.high-contrast) {
		--color-bg-0: #000000;
		--color-bg-1: #000000;
		--color-bg-2: #000000;
		--color-text: #ffffff;
		--color-theme-1: #ffff00;
		--color-theme-2: #00ffff;
	}

	:global(.high-contrast) .app {
		background-color: #000000;
		color: #ffffff;
	}

	:global(.high-contrast) a {
		color: #ffff00;
	}

	:global(.large-text) {
		font-size: 1.2rem;
	}

	:global(.large-text) h1 {
		font-size: 2.8rem;
	}

	:global(.large-text) h2 {
		font-size: 2.2rem;
	}

	:global(.simplified-interface) .complex-ui {
		display: none;
	}

	@media (min-width: 480px) {
		footer {
			padding: 12px 0;
		}
	}
</style>
