<script>
	import { createEventDispatcher } from 'svelte';

	const dispatch = createEventDispatcher();

	// Props
	export let accept = 'image/*';
	export let maxSize = 10 * 1024 * 1024; // 10MB default
	export let disabled = false;

	// State
	let fileInput;
	let dragOver = false;
	let uploading = false;
	let error = null;
	let previewUrl = null;
	let selectedFile = null;

	// Handle file selection
	function handleFileSelect(event) {
		const files = event.target.files || event.dataTransfer?.files;
		if (files && files.length > 0) {
			processFile(files[0]);
		}
	}

	// Process selected file
	function processFile(file) {
		error = null;

		// Validate file type
		if (!file.type.startsWith('image/')) {
			error = 'Please select an image file';
			return;
		}

		// Validate file size
		if (file.size > maxSize) {
			error = `File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`;
			return;
		}

		selectedFile = file;

		// Create preview
		const reader = new FileReader();
		reader.onload = (e) => {
			previewUrl = e.target.result;
		};
		reader.readAsDataURL(file);
	}

	// Upload file
	async function uploadFile() {
		if (!selectedFile) return;

		uploading = true;
		error = null;

		try {
			const formData = new FormData();
			formData.append('file', selectedFile);

			const response = await fetch('/api/admin/hero-images/upload', {
				method: 'POST',
				body: formData
			});

			if (!response.ok) {
				throw new Error('Upload failed');
			}

			const result = await response.json();

			if (result.success) {
				dispatch('upload', {
					imageUrl: result.data.imageUrl,
					thumbnailUrl: result.data.thumbnailUrl,
					filename: result.data.filename
				});
				clearSelection();
			} else {
				throw new Error(result.error || 'Upload failed');
			}
		} catch (err) {
			console.error('Upload error:', err);
			error = err.message || 'Upload failed';
		} finally {
			uploading = false;
		}
	}

	// Clear selection
	function clearSelection() {
		selectedFile = null;
		previewUrl = null;
		error = null;
		if (fileInput) {
			fileInput.value = '';
		}
	}

	// Drag and drop handlers
	function handleDragOver(event) {
		event.preventDefault();
		if (!disabled) {
			dragOver = true;
		}
	}

	function handleDragLeave(event) {
		event.preventDefault();
		dragOver = false;
	}

	function handleDrop(event) {
		event.preventDefault();
		dragOver = false;
		if (!disabled) {
			handleFileSelect(event);
		}
	}

	// Trigger file input
	function triggerFileInput() {
		if (!disabled && fileInput) {
			fileInput.click();
		}
	}
</script>

<div class="image-uploader">
	<input
		bind:this={fileInput}
		type="file"
		{accept}
		on:change={handleFileSelect}
		style="display: none;"
		{disabled}
	/>

	{#if previewUrl}
		<!-- Preview Section -->
		<div class="preview-section">
			<div class="preview-image">
				<img src={previewUrl} alt="Preview" />
			</div>
			<div class="preview-info">
				<h4>{selectedFile?.name}</h4>
				<p>Size: {Math.round((selectedFile?.size || 0) / 1024)} KB</p>
				{#if error}
					<p class="error-text">{error}</p>
				{/if}
			</div>
			<div class="preview-actions">
				<button 
					class="btn primary" 
					on:click={uploadFile}
					disabled={uploading || !!error}
				>
					{uploading ? 'Uploading...' : 'Upload Image'}
				</button>
				<button 
					class="btn secondary" 
					on:click={clearSelection}
					disabled={uploading}
				>
					Remove
				</button>
			</div>
		</div>
	{:else}
		<!-- Upload Area -->
		<div 
			class="upload-area"
			class:drag-over={dragOver}
			class:disabled
			on:click={triggerFileInput}
			on:dragover={handleDragOver}
			on:dragleave={handleDragLeave}
			on:drop={handleDrop}
		>
			<div class="upload-content">
				<div class="upload-icon">📁</div>
				<h3>Upload New Image</h3>
				<p>Click to select or drag and drop an image file</p>
				<p class="file-info">
					Supported formats: JPG, PNG, GIF, WebP<br>
					Max size: {Math.round(maxSize / 1024 / 1024)}MB
				</p>
			</div>
		</div>
	{/if}

	{#if error && !previewUrl}
		<div class="error-message">
			<p>{error}</p>
		</div>
	{/if}
</div>

<style>
	.image-uploader {
		width: 100%;
	}

	.upload-area {
		border: 2px dashed #ddd;
		border-radius: 8px;
		padding: 2rem;
		text-align: center;
		cursor: pointer;
		transition: all 0.2s ease;
		background-color: #fafafa;
	}

	.upload-area:hover:not(.disabled) {
		border-color: #4caf50;
		background-color: #f0f8f0;
	}

	.upload-area.drag-over {
		border-color: #4caf50;
		background-color: #e8f5e8;
	}

	.upload-area.disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.upload-content h3 {
		margin: 1rem 0 0.5rem 0;
		color: #333;
	}

	.upload-content p {
		margin: 0.5rem 0;
		color: #666;
	}

	.upload-icon {
		font-size: 3rem;
		margin-bottom: 1rem;
	}

	.file-info {
		font-size: 0.9rem;
		color: #888;
	}

	.preview-section {
		border: 1px solid #ddd;
		border-radius: 8px;
		overflow: hidden;
		background: white;
	}

	.preview-image {
		height: 200px;
		overflow: hidden;
		background-color: #f5f5f5;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.preview-image img {
		max-width: 100%;
		max-height: 100%;
		object-fit: contain;
	}

	.preview-info {
		padding: 1rem;
		border-bottom: 1px solid #eee;
	}

	.preview-info h4 {
		margin: 0 0 0.5rem 0;
		font-size: 1rem;
		word-break: break-all;
	}

	.preview-info p {
		margin: 0.25rem 0;
		font-size: 0.9rem;
		color: #666;
	}

	.error-text {
		color: #c62828 !important;
		font-weight: bold;
	}

	.preview-actions {
		padding: 1rem;
		display: flex;
		gap: 1rem;
		justify-content: flex-end;
	}

	.error-message {
		margin-top: 1rem;
		padding: 1rem;
		background-color: #ffebee;
		color: #c62828;
		border-radius: 4px;
		text-align: center;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		transition: opacity 0.2s ease;
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.btn.primary {
		background-color: #4caf50;
		color: white;
	}

	.btn.secondary {
		background-color: #f0f0f0;
		color: #333;
	}

	.btn:hover:not(:disabled) {
		opacity: 0.9;
	}
</style>
