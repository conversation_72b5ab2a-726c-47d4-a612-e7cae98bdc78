import {
	sqliteTable,
	text,
	integer,
	blob,
	primaryKey
} from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

import { relations } from 'drizzle-orm';

export const users = sqliteTable('users', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	username: text('username').notNull().unique(),
	displayName: text('display_name').notNull(),
	email: text('email').notNull().unique(),
	passwordHash: text('password_hash').notNull(),
	role: text('role', { enum: ['admin', 'moderator', 'user'] }).default('user').notNull(),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	preferences: text('preferences', { mode: 'json' }).$type<{
		highContrast: boolean;
		largeText: boolean;
		simplifiedInterface: boolean
	 }>().default('{"highContrast": false, "largeText": false, "simplifiedInterface": false}')
});

export const news = sqliteTable('news', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	title: text('title').notNull(),
	content: text('content').notNull(),
	imageUrl: text('image_url'),
	authorId: integer('author_id').references(() => users.id),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	published: integer('published', { mode: 'boolean' }).notNull().default(false)
});

export const gallery = sqliteTable('gallery', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	title: text('title').notNull(),
	description: text('description'),
	imageUrl: text('image_url').notNull(),
	thumbnailUrl: text('thumbnail_url').notNull(),
	authorId: integer('author_id').references(() => users.id),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	published: integer('published', { mode: 'boolean' }).notNull().default(false)
});

export const messages = sqliteTable('messages', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	userId: integer('user_id').references(() => users.id),
	content: text('content').notNull(),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	approved: integer('approved', { mode: 'boolean' }).notNull().default(false)
});

export const replies = sqliteTable('replies', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	messageId: integer('message_id').references(() => messages.id).notNull(),
	userId: integer('user_id').references(() => users.id),
	content: text('content').notNull(),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	approved: integer('approved', { mode: 'boolean' }).notNull().default(false)
});

export const comments = sqliteTable('comments', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	userId: integer('user_id').references(() => users.id),
	content: text('content').notNull(),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	approved: integer('approved', { mode: 'boolean' }).notNull().default(false),
	itemType: text('item_type', { enum: ['news', 'gallery'] }).notNull(),
	itemId: integer('item_id').notNull()
});

export const siteSettings = sqliteTable('site_settings', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	category: text('category', {
		enum: [
			'general',
			'appearance',
			'accessibility',
			'social'
		]
	}).notNull(),
	settings: text('settings', { mode: 'json' }).notNull()
});

export const messageOfTheDay = sqliteTable('message_of_the_day', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	content: text('content').notNull(),
	active: integer('active', { mode: 'boolean' }).notNull().default(true),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

export const media = sqliteTable('media', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	filename: text('filename').notNull(),
	originalName: text('original_name').notNull(),
	path: text('path').notNull(),
	thumbnailPath: text('thumbnail_path'),
	type: text('type').notNull(), // image, video, document, etc.
	mimeType: text('mime_type').notNull(),
	size: integer('size').notNull(),
	width: integer('width'),
	height: integer('height'),
	alt: text('alt'),
	caption: text('caption'),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	authorId: integer('author_id').references(() => users.id)
});

export const usersRelations = relations(users, ({ many }) => ({
	news: many(news),
	gallery: many(gallery),
	messages: many(messages),
	replies: many(replies),
	comments: many(comments),
	media: many(media)
}));

export const newsRelations = relations(news, ({ one, many }) => ({
	author: one(users, {
		fields: [news.authorId],
		references: [users.id]
	}),
	comments: many(comments, {
		filterForeignFields: (comment) => comment.itemType.equals('news')
	})
}));

export const galleryRelations = relations(gallery, ({ one, many }) => ({
	author: one(users, {
		fields: [gallery.authorId],
		references: [users.id]
	}),
	comments: many(comments, {
		filterForeignFields: (comment) => comment.itemType.equals('gallery')
	})
}));

export const messagesRelations = relations(messages, ({ one, many }) => ({
	user: one(users, {
		fields: [messages.userId],
		references: [users.id]
	}),
	replies: many(replies)
}));

export const repliesRelations = relations(replies, ({ one }) => ({
	message: one(messages, {
		fields: [replies.messageId],
		references: [messages.id]
	}),
	user: one(users, {
		fields: [replies.userId],
		references: [users.id]
	})
}));

export const commentsRelations = relations(comments, ({ one }) => ({
	user: one(users, {
		fields: [comments.userId],
		references: [users.id]
	})
}));

export const mediaRelations = relations(media, ({ one }) => ({
	author: one(users, {
		fields: [media.authorId],
		references: [users.id]
	})
}));

export const user = sqliteTable('user', {
	id: integer('id').primaryKey(),
	age: integer('age')
});
