import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { gallery } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';

// GET /api/gallery - Get all published gallery items
export const GET: RequestHandler = async ({ url }) => {
  try {
    // Get query parameters
    const limit = Number(url.searchParams.get('limit') || '20');
    const offset = Number(url.searchParams.get('offset') || '0');
    
    // Fetch gallery items from the database
    const items = await db.select()
      .from(gallery)
      .where(eq(gallery.published, true))
      .limit(limit)
      .offset(offset)
      .orderBy(gallery.createdAt);
    
    return json({
      success: true,
      data: items
    });
  } catch (error) {
    console.error('Error fetching gallery items:', error);
    return json({
      success: false,
      error: 'Failed to fetch gallery items'
    }, { status: 500 });
  }
};

// POST /api/gallery - Create a new gallery item (admin only)
export const POST: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }
  
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.title || !body.imageUrl || !body.thumbnailUrl) {
      return json({
        success: false,
        error: 'Title, imageUrl, and thumbnailUrl are required'
      }, { status: 400 });
    }
    
    // Insert new gallery item into the database
    const result = await db.insert(gallery).values({
      title: body.title,
      description: body.description || null,
      imageUrl: body.imageUrl,
      thumbnailUrl: body.thumbnailUrl,
      authorId: locals.user.id,
      published: body.published || false
    }).returning();
    
    return json({
      success: true,
      data: result[0]
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating gallery item:', error);
    return json({
      success: false,
      error: 'Failed to create gallery item'
    }, { status: 500 });
  }
};
