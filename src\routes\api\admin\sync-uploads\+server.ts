import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import fs from 'fs';
import path from 'path';
import { db } from '$lib/server/db';
import { gallery, media } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';

// POST /api/admin/sync-uploads - Sync uploaded files with database (admin only)
export const POST: RequestHandler = async ({ locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }

  try {
    const UPLOAD_DIR = path.resolve('static/uploads/gallery');
    const THUMBNAIL_DIR = path.join(UPLOAD_DIR, 'thumbnails');

    // Check if directories exist
    if (!fs.existsSync(UPLOAD_DIR)) {
      return json({
        success: false,
        error: 'Upload directory does not exist'
      }, { status: 404 });
    }

    // Get all files in the gallery directory (excluding thumbnails)
    const files = fs.readdirSync(UPLOAD_DIR).filter(file => {
      const filePath = path.join(UPLOAD_DIR, file);
      const stat = fs.statSync(filePath);
      return stat.isFile() && !file.startsWith('.') && file !== 'thumbnails';
    });

    const results = {
      processed: 0,
      created: 0,
      skipped: 0,
      errors: []
    };

    for (const filename of files) {
      try {
        results.processed++;

        const filePath = path.join(UPLOAD_DIR, filename);
        const stat = fs.statSync(filePath);

        // Check if this file already exists in the media table
        const existingMedia = await db.select()
          .from(media)
          .where(eq(media.filename, filename))
          .limit(1);

        // Check if this file already exists in the gallery table
        const existingGallery = await db.select()
          .from(gallery)
          .where(eq(gallery.imageUrl, `/uploads/gallery/${filename}`))
          .limit(1);

        if (existingMedia.length > 0 || existingGallery.length > 0) {
          results.skipped++;
          continue;
        }

        // Determine file type
        const ext = path.extname(filename).toLowerCase();
        const isImage = ['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext);

        if (!isImage) {
          results.skipped++;
          continue;
        }

        // Get file info
        const mimeType = getMimeType(ext);
        const imageUrl = `/uploads/gallery/${filename}`;
        const thumbnailUrl = `/uploads/gallery/thumbnails/${filename}`;

        // Create media entry
        const mediaResult = await db.insert(media).values({
          filename: filename,
          originalName: filename,
          path: imageUrl,
          thumbnailPath: thumbnailUrl,
          type: 'image',
          mimeType: mimeType,
          size: stat.size,
          authorId: locals.user.id
        }).returning();

        // Create gallery entry
        const title = generateTitleFromFilename(filename);
        await db.insert(gallery).values({
          title: title,
          description: `Uploaded image: ${filename}`,
          imageUrl: imageUrl,
          thumbnailUrl: thumbnailUrl,
          published: false // Default to unpublished so admin can review
        });

        results.created++;
      } catch (error) {
        console.error(`Error processing file ${filename}:`, error);
        results.errors.push(`${filename}: ${error.message}`);
      }
    }

    return json({
      success: true,
      data: results
    });
  } catch (error) {
    console.error('Error syncing uploads:', error);
    return json({
      success: false,
      error: 'Failed to sync uploads'
    }, { status: 500 });
  }
};

// Helper function to get MIME type from extension
function getMimeType(ext: string): string {
  const mimeTypes: Record<string, string> = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp'
  };
  return mimeTypes[ext] || 'application/octet-stream';
}

// Helper function to generate a title from filename
function generateTitleFromFilename(filename: string): string {
  // Remove extension and timestamp/hash suffixes
  let title = path.parse(filename).name;

  // Remove timestamp patterns like -1747419712429-rrsvbbf1
  title = title.replace(/-\d{13}-[a-z0-9]+$/i, '');

  // Replace hyphens and underscores with spaces
  title = title.replace(/[-_]/g, ' ');

  // Capitalize first letter of each word
  title = title.replace(/\b\w/g, l => l.toUpperCase());

  return title || 'Untitled Image';
}
